"""
Comprehensive tests for the AI Resource Enhancement Pipeline.
Tests the complete end-to-end pipeline flow and individual components.
"""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID

import pytest

from arep.api.models import Resource, ResourceSubmissionResponse, ToolDetails
from arep.models import (ClassificationResult, ClassifiedEntity, MinimalEntity,
                         ResearchData)
from arep.pipeline import EnhancementPipeline


@pytest.fixture
def sample_minimal_entity():
    """Create a sample minimal entity for testing."""
    return MinimalEntity(
        name="Test AI Tool",
        url="https://testaitool.com",
        logo_url="https://testaitool.com/logo.png",
        source="test_scraper",
        discovered_at=datetime.now(),
    )


@pytest.fixture
def sample_classification_result():
    """Create a sample classification result."""
    return ClassificationResult(
        entity_type="tool",
        entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
        confidence=0.95,
        reasoning="Strong indicators of AI tool functionality",
        alternative_types=["software"],
    )


@pytest.fixture
def sample_research_data():
    """Create sample research data."""
    return ResearchData(
        description="A comprehensive AI tool for automation and analysis",
        short_description="AI automation tool",
        features=["Machine Learning", "API Integration", "Real-time Analytics"],
        categories=["AI Tools", "Automation"],
        tags=["ai", "automation", "analytics"],
        pricing_info="Freemium",
        contact_info="<EMAIL>",
        social_links={"twitter": "https://twitter.com/testaitool"},
        technical_details={"api": True, "platforms": ["web", "mobile"]},
        research_sources=["https://testaitool.com"],
        research_timestamp=datetime.now(),
    )


@pytest.fixture
def sample_resource():
    """Create a sample resource for API submission."""
    return Resource(
        name="Test AI Tool",
        website_url="https://testaitool.com",
        entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
        short_description="AI automation tool",
        logo_url="https://testaitool.com/logo.png",
        tool_details=ToolDetails(
            key_features=["Machine Learning", "API Integration"],
            has_api=True,
            has_free_tier=True,
        ),
    )


@pytest.fixture
def mock_pipeline_components():
    """Create mocked pipeline components."""
    with patch.multiple(
        "arep.pipeline",
        MinimalDataCollector=MagicMock(),
        EntityTypeClassifier=MagicMock(),
        SmartResearchEngine=MagicMock(),
        AINavigatorClient=MagicMock(),
        MetricsCollector=MagicMock(),
    ) as mocks:
        yield mocks


class TestEnhancementPipeline:
    """Test cases for the main enhancement pipeline."""

    @pytest.mark.asyncio
    async def test_pipeline_initialization(self):
        """Test pipeline initialization with default config."""
        pipeline = EnhancementPipeline()

        assert pipeline.max_concurrent_entities == 5
        assert pipeline.skip_failed_classification is True
        assert pipeline.skip_unsupported_types is True
        assert len(pipeline.processing_status) == 0
        assert len(pipeline.processed_entities) == 0
        assert len(pipeline.failed_entities) == 0

    @pytest.mark.asyncio
    async def test_pipeline_initialization_with_config(self):
        """Test pipeline initialization with custom config."""
        config = {
            "max_concurrent_entities": 10,
            "skip_failed_classification": False,
            "skip_unsupported_types": False,
        }
        pipeline = EnhancementPipeline(config=config)

        assert pipeline.max_concurrent_entities == 10
        assert pipeline.skip_failed_classification is False
        assert pipeline.skip_unsupported_types is False

    @pytest.mark.asyncio
    async def test_single_entity_processing_success(
        self,
        sample_minimal_entity,
        sample_classification_result,
        sample_research_data,
        sample_resource,
        mock_pipeline_components,
    ):
        """Test successful processing of a single entity."""
        # Setup mocks
        pipeline = EnhancementPipeline()

        # Mock classification
        pipeline.classifier.classify = AsyncMock(
            return_value=sample_classification_result
        )

        # Mock research
        pipeline.research_engine.__aenter__ = AsyncMock(
            return_value=pipeline.research_engine
        )
        pipeline.research_engine.__aexit__ = AsyncMock(return_value=None)
        pipeline.research_engine.research = AsyncMock(return_value=sample_research_data)

        # Mock enhancer
        with patch("arep.enhancement.registry.enhancer_registry") as mock_registry:
            mock_enhancer = AsyncMock()
            mock_enhancer.enhance = AsyncMock(return_value=sample_resource)
            mock_registry.has_enhancer.return_value = True
            mock_registry.get_enhancer.return_value = mock_enhancer

            # Mock API client
            pipeline.api_client.__aenter__ = AsyncMock(return_value=pipeline.api_client)
            pipeline.api_client.__aexit__ = AsyncMock(return_value=None)
            pipeline.api_client.submit_resource = AsyncMock(
                return_value=ResourceSubmissionResponse(
                    success=True,
                    message="Resource submitted successfully",
                    entity_id="a1b2c3d4-e5f6-7890-abcd-123456789012",
                )
            )

            # Process entity
            result = await pipeline.process_single_entity(sample_minimal_entity)

            # Verify result
            assert result is not None
            assert result["entity_name"] == "Test AI Tool"
            assert result["entity_type"] == "tool"
            assert result["classification_confidence"] == 0.95
            assert "processing_time" in result

            # Verify mocks were called
            pipeline.classifier.classify.assert_called_once()
            pipeline.research_engine.research.assert_called_once()
            mock_enhancer.enhance.assert_called_once()
            pipeline.api_client.submit_resource.assert_called_once()

    @pytest.mark.asyncio
    async def test_single_entity_processing_classification_failure(
        self, sample_minimal_entity, mock_pipeline_components
    ):
        """Test entity processing with classification failure."""
        pipeline = EnhancementPipeline()

        # Mock failed classification
        pipeline.classifier.classify = AsyncMock(
            side_effect=Exception("Classification failed")
        )

        # Process entity
        result = await pipeline.process_single_entity(sample_minimal_entity)

        # Should return None due to classification failure
        assert result is None
        assert len(pipeline.failed_entities) == 1

    @pytest.mark.asyncio
    async def test_single_entity_processing_unsupported_type(
        self,
        sample_minimal_entity,
        sample_classification_result,
        mock_pipeline_components,
    ):
        """Test entity processing with unsupported entity type."""
        pipeline = EnhancementPipeline()

        # Mock classification with unsupported type
        unsupported_classification = ClassificationResult(
            entity_type="unsupported_type",
            entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
            confidence=0.95,
        )
        pipeline.classifier.classify = AsyncMock(
            return_value=unsupported_classification
        )

        # Mock enhancer registry to return False for unsupported type
        with patch("arep.enhancement.registry.enhancer_registry") as mock_registry:
            mock_registry.has_enhancer.return_value = False

            # Process entity
            result = await pipeline.process_single_entity(sample_minimal_entity)

            # Should return None due to unsupported type
            assert result is None
            assert len(pipeline.failed_entities) == 1

    @pytest.mark.asyncio
    async def test_full_pipeline_run_empty_collection(self, mock_pipeline_components):
        """Test full pipeline run with empty entity collection."""
        pipeline = EnhancementPipeline()

        # Mock empty collection
        pipeline.collector.collect = AsyncMock(return_value=[])

        # Run pipeline
        results = await pipeline.run()

        # Verify results
        assert results["pipeline_completed"] is True
        assert results["entities_processed"] == 0
        assert results["entities_successful"] == 0
        assert results["entities_failed"] == 0

    @pytest.mark.asyncio
    async def test_full_pipeline_run_with_entities(
        self,
        sample_minimal_entity,
        sample_classification_result,
        sample_research_data,
        sample_resource,
        mock_pipeline_components,
    ):
        """Test full pipeline run with successful entity processing."""
        pipeline = EnhancementPipeline()

        # Mock collection
        pipeline.collector.collect = AsyncMock(return_value=[sample_minimal_entity])

        # Mock all pipeline stages
        pipeline.classifier.classify = AsyncMock(
            return_value=sample_classification_result
        )

        pipeline.research_engine.__aenter__ = AsyncMock(
            return_value=pipeline.research_engine
        )
        pipeline.research_engine.__aexit__ = AsyncMock(return_value=None)
        pipeline.research_engine.research = AsyncMock(return_value=sample_research_data)

        with patch("arep.enhancement.registry.enhancer_registry") as mock_registry:
            mock_enhancer = AsyncMock()
            mock_enhancer.enhance = AsyncMock(return_value=sample_resource)
            mock_registry.has_enhancer.return_value = True
            mock_registry.get_enhancer.return_value = mock_enhancer

            pipeline.api_client.__aenter__ = AsyncMock(return_value=pipeline.api_client)
            pipeline.api_client.__aexit__ = AsyncMock(return_value=None)
            pipeline.api_client.submit_resource = AsyncMock(
                return_value=ResourceSubmissionResponse(
                    success=True,
                    message="Resource submitted successfully",
                    entity_id="a1b2c3d4-e5f6-7890-abcd-123456789012",
                )
            )

            # Run pipeline
            results = await pipeline.run()

            # Verify results
            assert results["pipeline_completed"] is True
            assert results["entities_processed"] == 1
            assert results["entities_successful"] == 1
            assert results["entities_failed"] == 0
            assert results["success_rate"] == 1.0
            assert len(results["results"]) == 1

    @pytest.mark.asyncio
    async def test_concurrent_processing(
        self,
        sample_classification_result,
        sample_research_data,
        sample_resource,
        mock_pipeline_components,
    ):
        """Test concurrent processing of multiple entities."""
        # Create multiple test entities
        entities = [
            MinimalEntity(
                name=f"Test Tool {i}",
                url=f"https://testtool{i}.com",
                source="test_scraper",
                discovered_at=datetime.now(),
            )
            for i in range(3)
        ]

        pipeline = EnhancementPipeline(config={"max_concurrent_entities": 2})

        # Mock all pipeline stages
        pipeline.classifier.classify = AsyncMock(
            return_value=sample_classification_result
        )

        pipeline.research_engine.__aenter__ = AsyncMock(
            return_value=pipeline.research_engine
        )
        pipeline.research_engine.__aexit__ = AsyncMock(return_value=None)
        pipeline.research_engine.research = AsyncMock(return_value=sample_research_data)

        with patch("arep.enhancement.registry.enhancer_registry") as mock_registry:
            mock_enhancer = AsyncMock()
            mock_enhancer.enhance = AsyncMock(return_value=sample_resource)
            mock_registry.has_enhancer.return_value = True
            mock_registry.get_enhancer.return_value = mock_enhancer

            pipeline.api_client.__aenter__ = AsyncMock(return_value=pipeline.api_client)
            pipeline.api_client.__aexit__ = AsyncMock(return_value=None)
            pipeline.api_client.submit_resource = AsyncMock(
                return_value=ResourceSubmissionResponse(
                    success=True,
                    message="Resource submitted successfully",
                    entity_id="a1b2c3d4-e5f6-7890-abcd-123456789012",
                )
            )

            # Process entities concurrently
            results = await pipeline._process_entities_concurrent(entities)

            # Verify all entities were processed
            assert len(results) == 3
            for result in results:
                assert result["entity_type"] == "tool"
                assert result["classification_confidence"] == 0.95
