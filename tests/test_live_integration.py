"""
Live integration test for the AI Navigator API submission.

This test submits a REAL entity to the staging database and then attempts cleanup.
It verifies real-world connectivity, authentication, and schema matching.

IMPORTANT: This test requires staging environment credentials and should NOT be run
in CI/CD pipelines without proper configuration.
"""

import os
from datetime import datetime
from uuid import UUID

import pytest

from arep.api.client import AINavigatorClient
from arep.api.models import Resource, ToolDetails

# This marker will skip the test unless a specific environment variable is set.
# This prevents it from running accidentally in CI/CD pipelines without credentials.
requires_live_env = pytest.mark.skipif(
    os.getenv("RUN_LIVE_TESTS") != "true",
    reason="This test requires a live staging environment and credentials. Set RUN_LIVE_TESTS=true to run.",
)


@requires_live_env
@pytest.mark.asyncio
async def test_live_db_submission():
    """
    This test submits a REAL entity to the staging database and then attempts cleanup.
    It verifies real-world connectivity, authentication, and schema matching.

    Prerequisites:
    - Set RUN_LIVE_TESTS=true environment variable
    - Set AI_NAV_API_URL to staging environment URL
    - Set AI_NAV_AUTH_TOKEN to valid staging auth token
    """
    # --- ARRANGE (Build a sample payload) ---

    # We don't need to run the full scraper pipeline here.
    # We just need a valid payload to test the submission logic.
    # Use timestamp to ensure unique entity names
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    test_resource = Resource(
        name=f"[TEST] Live Integration Tool {timestamp}",
        website_url="https://example-test.com/live-integration",
        entity_type_id=UUID(
            "fd181400-c9e6-431c-a8bd-c068d0491aba"
        ),  # AI Tool entity type UUID
        short_description="A temporary tool for live integration testing.",
        logo_url="https://www.google.com/s2/favicons?domain=example.com&sz=128",
        status="PENDING",
        category_ids=[UUID("bfc51175-6f22-47a9-a602-9d85c7aa340a")],  # Analytics category
        tag_ids=[UUID("2cef8b3a-8760-4e55-ab73-2fd9993acfe3")],  # Advanced tag
        tool_details=ToolDetails(
            has_free_tier=True,
            pricing_tier="free",
            key_features=["Integration testing", "Temporary entity", "Cleanup test"],
            use_cases=["Testing"],
            integrations=["API"],
            programming_languages=["Python"],
            frameworks=["FastAPI"],
            libraries=["aiohttp"],
            target_audience=["Developers"],
            deployment_options=["Cloud"],
            supported_os=["Linux"],
            support_channels=["Email"]
        ),
    )

    client = AINavigatorClient()
    created_entity_id = None

    # --- ACT & ASSERT ---
    try:
        # 1. SUBMIT the entity to the live staging database
        print(f"\nAttempting to submit entity to staging DB: {client.api_url}")
        print(f"Entity name: {test_resource.name}")

        result = await client.submit_resource(test_resource)
        print(f"Submission result: {result}")

        # Verify successful submission
        assert result.success is True, f"Submission failed: {result.message}"
        assert (
            result.entity_id is not None
        ), "No entity ID returned from successful submission"

        created_entity_id = result.entity_id
        print(f"✅ Successfully created entity with ID: {created_entity_id}")

        # 2. VERIFY the entity exists by fetching its status
        print(f"Verifying entity exists by fetching status...")
        status_info = await client.get_submission_status(str(created_entity_id))
        print(f"Entity status: {status_info}")

        # Verify the returned data matches what we submitted
        assert status_info.get("name") == test_resource.name
        # API returns 'websiteUrl' (camelCase) instead of 'website_url' (snake_case)
        assert status_info.get("websiteUrl") == str(test_resource.website_url)
        # Check entity type in nested object
        entity_type = status_info.get("entityType", {})
        assert entity_type.get("id") == str(test_resource.entity_type_id)

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        raise

    finally:
        # 3. CLEANUP: Always attempt to delete the entity afterward.
        #    This is critical to keep the staging database clean.
        if created_entity_id:
            print(f"Attempting to clean up and delete entity ID: {created_entity_id}")
            try:
                # Attempt automatic cleanup using the delete method
                delete_result = await client.delete_resource(str(created_entity_id))

                if delete_result.success:
                    print("✅ Cleanup complete - entity deleted successfully.")
                else:
                    print(f"⚠️  Automatic cleanup failed: {delete_result.message}")
                    print(f"   MANUAL CLEANUP REQUIRED: Entity ID {created_entity_id}")
                    print(f"   Entity name: {test_resource.name}")
                    print(f"   Created at: {datetime.utcnow()}")

            except Exception as cleanup_error:
                error_msg = str(cleanup_error)
                if "authentication" in error_msg.lower() or "expired" in error_msg.lower():
                    print(f"⚠️  Cleanup failed due to token expiration: {cleanup_error}")
                    print(f"   This is expected for long-running tests (JWT tokens expire after 1 hour)")
                else:
                    print(f"❌ Cleanup failed with exception: {cleanup_error}")
                print(f"   MANUAL CLEANUP REQUIRED: Entity ID {created_entity_id}")
                print(f"   Entity name: {test_resource.name}")
                print(f"   Created at: {datetime.utcnow()}")
        else:
            print("No entity was created, no cleanup needed.")

        # Always close the client session
        await client.close()


@requires_live_env
@pytest.mark.asyncio
async def test_live_api_connectivity():
    """
    Simple connectivity test to verify the staging API is accessible.
    This test doesn't create any data, just checks if we can reach the API.
    """
    print(f"\nTesting connectivity to staging API...")

    client = AINavigatorClient()

    try:
        # Test basic connectivity
        is_healthy = await client.health_check()
        print(f"API health check result: {is_healthy}")

        assert (
            is_healthy is True
        ), "API health check failed - staging environment may be down"
        print("✅ Successfully connected to staging API")

    except Exception as e:
        print(f"❌ Connectivity test failed: {e}")
        raise

    finally:
        await client.close()


@requires_live_env
@pytest.mark.asyncio
async def test_live_authentication():
    """
    Test that our authentication token is valid for the staging environment.
    This test makes a minimal request to verify auth without creating data.
    """
    print(f"\nTesting authentication with staging API...")

    client = AINavigatorClient()

    try:
        # Try to make an authenticated request that doesn't create data
        # We'll try to get a non-existent entity, which should return 404 but prove auth works
        try:
            await client.get_submission_status("00000000-0000-0000-0000-000000000000")
        except Exception as e:
            # We expect this to fail (entity not found), but it should be an API error, not auth error
            error_message = str(e).lower()
            if (
                "unauthorized" in error_message
                or "forbidden" in error_message
                or "401" in error_message
                or "403" in error_message
            ):
                pytest.fail(f"Authentication failed: {e}")
            else:
                # Any other error (like 404) means auth worked
                print(f"Authentication successful (got expected error: {e})")

        print("✅ Authentication token is valid")

    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        raise

    finally:
        await client.close()


if __name__ == "__main__":
    """
    Allow running this test file directly for manual testing.
    Usage: RUN_LIVE_TESTS=true python tests/test_live_integration.py
    """
    import asyncio

    async def run_tests():
        print("Running live integration tests...")
        print("=" * 50)

        try:
            await test_live_api_connectivity()
            print("\n" + "=" * 50)

            await test_live_authentication()
            print("\n" + "=" * 50)

            await test_live_db_submission()
            print("\n" + "=" * 50)

            print("✅ All live integration tests passed!")

        except Exception as e:
            print(f"❌ Live integration tests failed: {e}")
            raise

    if os.getenv("RUN_LIVE_TESTS") == "true":
        asyncio.run(run_tests())
    else:
        print("Set RUN_LIVE_TESTS=true to run live integration tests")
