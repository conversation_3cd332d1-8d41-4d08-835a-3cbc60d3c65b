"""
Tests for the new pluggable collector system.
Verifies that the registry system works and collectors are automatically discovered.
"""

import asyncio
from datetime import datetime
from unittest.mock import AsyncMock, patch

import pytest

from arep.collectors import (PluggableDataCollector, get_all_collectors,
                             get_collector_by_name, list_available_collectors)
from arep.collectors.base import BaseCollector
from arep.models import MinimalEntity


class TestCollector(BaseCollector):
    """Test collector for testing the registry system."""

    source_name = "test_collector"

    async def collect(self, session):
        """Return test data."""
        return [
            MinimalEntity(
                name="Test Entity 1",
                url="https://test1.com",
                source=self.source_name,
                discovered_at=datetime.utcnow(),
            ),
            MinimalEntity(
                name="Test Entity 2",
                url="https://test2.com",
                source=self.source_name,
                discovered_at=datetime.utcnow(),
            ),
        ]


@pytest.mark.asyncio
async def test_registry_discovers_collectors():
    """Test that the registry system discovers available collectors."""
    collectors = get_all_collectors()

    # Should find at least the ProductHunt and ArXiv collectors
    assert len(collectors) >= 2

    collector_names = [c.source_name for c in collectors]
    assert "product_hunt" in collector_names
    assert "arxiv" in collector_names


@pytest.mark.asyncio
async def test_get_collector_by_name():
    """Test getting a specific collector by name."""
    # Test getting a known collector
    product_hunt_collector = get_collector_by_name("product_hunt")
    assert product_hunt_collector.source_name == "product_hunt"

    arxiv_collector = get_collector_by_name("arxiv")
    assert arxiv_collector.source_name == "arxiv"

    # Test getting a non-existent collector
    with pytest.raises(ValueError):
        get_collector_by_name("non_existent_collector")


@pytest.mark.asyncio
async def test_list_available_collectors():
    """Test listing all available collector names."""
    collector_names = list_available_collectors()

    assert isinstance(collector_names, list)
    assert len(collector_names) >= 2
    assert "product_hunt" in collector_names
    assert "arxiv" in collector_names


@pytest.mark.asyncio
async def test_pluggable_data_collector():
    """Test the new PluggableDataCollector class."""
    collector = PluggableDataCollector()

    # Should have discovered collectors
    assert len(collector.collectors) >= 2

    # Test getting collector names
    names = collector.get_collector_names()
    assert "product_hunt" in names
    assert "arxiv" in names


@pytest.mark.asyncio
async def test_pluggable_collector_collect():
    """Test that the pluggable collector can collect data."""
    # Mock the individual collectors to avoid making real HTTP requests
    with patch(
        "arep.collectors.product_hunt.ProductHuntScraper.collect"
    ) as mock_ph, patch("arep.collectors.arxiv.ArxivApiClient.collect") as mock_arxiv:
        # Mock return values
        mock_ph.return_value = [
            MinimalEntity(
                name="Mock Product Hunt Tool",
                url="https://mockph.com",
                source="product_hunt",
                discovered_at=datetime.utcnow(),
            )
        ]

        mock_arxiv.return_value = [
            MinimalEntity(
                name="Mock ArXiv Paper",
                url="https://arxiv.org/abs/mock",
                source="arxiv",
                discovered_at=datetime.utcnow(),
            )
        ]

        collector = PluggableDataCollector()
        entities = await collector.collect()

        # Should have collected entities from both sources
        assert len(entities) >= 2

        # Check that we have entities from both sources
        sources = {entity.source for entity in entities}
        assert "product_hunt" in sources
        assert "arxiv" in sources


@pytest.mark.asyncio
async def test_deduplication():
    """Test that the collector properly deduplicates entities."""
    with patch(
        "arep.collectors.product_hunt.ProductHuntScraper.collect"
    ) as mock_ph, patch("arep.collectors.arxiv.ArxivApiClient.collect") as mock_arxiv:
        # Create duplicate entities (same URL)
        duplicate_entity = MinimalEntity(
            name="Duplicate Tool",
            url="https://duplicate.com",
            source="product_hunt",
            discovered_at=datetime.utcnow(),
        )

        mock_ph.return_value = [duplicate_entity]
        mock_arxiv.return_value = [
            duplicate_entity
        ]  # Same entity from different source

        collector = PluggableDataCollector()
        entities = await collector.collect()

        # Should only have one entity after deduplication
        assert len(entities) == 1
        assert str(entities[0].url) == "https://duplicate.com/"


@pytest.mark.asyncio
async def test_collector_error_handling():
    """Test that the collector handles errors gracefully."""
    with patch(
        "arep.collectors.product_hunt.ProductHuntScraper.collect"
    ) as mock_ph, patch("arep.collectors.arxiv.ArxivApiClient.collect") as mock_arxiv:
        # Make one collector raise an exception
        mock_ph.side_effect = Exception("Network error")
        mock_arxiv.return_value = [
            MinimalEntity(
                name="Working ArXiv Paper",
                url="https://arxiv.org/abs/working",
                source="arxiv",
                discovered_at=datetime.utcnow(),
            )
        ]

        collector = PluggableDataCollector()
        entities = await collector.collect()

        # Should still get entities from the working collector
        assert len(entities) >= 1
        assert any(entity.source == "arxiv" for entity in entities)


@pytest.mark.asyncio
async def test_base_collector_interface():
    """Test that BaseCollector subclasses work correctly."""
    test_collector = TestCollector()

    # Test the run method
    entities = await test_collector.run()

    assert len(entities) == 2
    assert all(isinstance(entity, MinimalEntity) for entity in entities)
    assert all(entity.source == "test_collector" for entity in entities)


if __name__ == "__main__":
    # Run a simple test
    async def main():
        print("Testing pluggable collector system...")

        # Test registry
        collectors = get_all_collectors()
        print(
            f"Found {len(collectors)} collectors: {[c.source_name for c in collectors]}"
        )

        # Test pluggable collector
        collector = PluggableDataCollector()
        print(
            f"PluggableDataCollector initialized with: {collector.get_collector_names()}"
        )

        print("All tests passed!")

    asyncio.run(main())
