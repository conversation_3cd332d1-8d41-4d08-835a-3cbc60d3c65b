"""
Tests for the Smart Research Engine.
"""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID

import aiohttp
import pytest
from bs4 import BeautifulSoup

from arep.enhancement.research import SmartResearchEngine
from arep.models import ClassifiedEntity


@pytest.fixture
def sample_classified_entity():
    """Create a sample classified entity for testing."""
    return ClassifiedEntity(
        name="Test AI Tool",
        url="https://testaitool.com",
        logo_url="https://testaitool.com/logo.png",
        source="test_scraper",
        discovered_at=datetime.now(),
        entity_type="tool",
        entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
        classification_confidence=0.95,
        classification_reasoning="Strong AI tool indicators",
        alternative_types=["software"],
    )


@pytest.fixture
def sample_html_content():
    """Sample HTML content for testing."""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test AI Tool</title>
        <meta name="description" content="A powerful AI tool for automation and analysis">
        <meta property="og:description" content="AI automation made simple">
        <meta name="keywords" content="ai, automation, machine learning, analytics">
    </head>
    <body>
        <h1>Test AI Tool</h1>
        <p>Our tool offers advanced machine learning capabilities and real-time analytics.</p>
        <div class="features">
            <h2>Key Features</h2>
            <ul>
                <li>Machine Learning Automation</li>
                <li>Real-time Data Processing</li>
                <li>API Integration</li>
            </ul>
        </div>
        <div class="pricing">
            <h2>Pricing</h2>
            <p>Free tier available with premium features starting at $29/month</p>
        </div>
        <div class="contact">
            <a href="mailto:<EMAIL>">Contact Support</a>
            <a href="https://twitter.com/testaitool">Follow us on Twitter</a>
            <a href="https://github.com/testaitool">GitHub Repository</a>
        </div>
    </body>
    </html>
    """


class TestSmartResearchEngine:
    """Test cases for the SmartResearchEngine."""

    @pytest.mark.asyncio
    async def test_research_engine_initialization(self):
        """Test research engine initialization."""
        engine = SmartResearchEngine()

        assert engine.session is None  # Should be None until context manager is used

    @pytest.mark.asyncio
    async def test_context_manager(self):
        """Test async context manager functionality."""
        engine = SmartResearchEngine()

        async with engine as research_engine:
            assert research_engine.session is not None
            assert isinstance(research_engine.session, aiohttp.ClientSession)

        # Session should be closed after exiting context
        assert research_engine.session.closed

    @pytest.mark.asyncio
    async def test_research_success(
        self, sample_classified_entity, sample_html_content
    ):
        """Test successful research operation."""
        engine = SmartResearchEngine()

        # Mock HTTP response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value=sample_html_content)

        # Mock content extraction method
        mock_content_data = {
            "title": "Test AI Tool",
            "description": "A powerful AI tool for automation",
            "features": ["Machine Learning", "Real-time Analytics"],
        }

        with patch.object(engine, "_extract_content_from_soup") as mock_extract:
            mock_extract.return_value = mock_content_data

            async with engine as research_engine:
                # Mock the session.get method
                research_engine.session.get = AsyncMock(return_value=mock_response)

                # Perform research
                research_data = await research_engine.research(sample_classified_entity)

                # Verify research data
                assert research_data is not None
                assert (
                    research_data.description
                    == "A powerful AI tool for automation and analysis"
                )
                assert research_data.short_description == "AI automation made simple"
                assert "ai" in research_data.tags
                assert research_data.pricing_info == "Freemium"
                assert research_data.contact_info == "<EMAIL>"
                assert "twitter" in research_data.social_links
                assert len(research_data.research_sources) > 0

    @pytest.mark.asyncio
    async def test_research_http_error(self, sample_classified_entity):
        """Test research with HTTP error."""
        engine = SmartResearchEngine()

        # Mock HTTP error response
        mock_response = AsyncMock()
        mock_response.status = 404

        async with engine as research_engine:
            research_engine.session.get = AsyncMock(return_value=mock_response)

            # Perform research
            research_data = await research_engine.research(sample_classified_entity)

            # Should return minimal research data on failure
            assert research_data is not None
            assert "Research failed" in research_data.short_description

    @pytest.mark.asyncio
    async def test_research_exception_handling(self, sample_classified_entity):
        """Test research with exception handling."""
        engine = SmartResearchEngine()

        async with engine as research_engine:
            # Mock session to raise exception
            research_engine.session.get = AsyncMock(
                side_effect=Exception("Network error")
            )

            # Perform research
            research_data = await research_engine.research(sample_classified_entity)

            # Should return minimal research data on failure
            assert research_data is not None
            assert "Research failed" in research_data.short_description

    @pytest.mark.asyncio
    async def test_website_research_success(self, sample_html_content):
        """Test successful website research."""
        engine = SmartResearchEngine()

        # Mock HTTP response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value=sample_html_content)

        # Mock content extraction method
        mock_content_data = {
            "title": "Test AI Tool",
            "description": "A powerful AI tool",
            "features": ["Machine Learning"],
        }

        with patch.object(engine, "_extract_content_from_soup") as mock_extract:
            mock_extract.return_value = mock_content_data

            async with engine as research_engine:
                research_engine.session.get = AsyncMock(return_value=mock_response)

                # Test website research
                website_data = await research_engine._research_website(
                    "https://testaitool.com", "Test AI Tool"
                )

                # Verify extracted data
                assert website_data is not None
                assert "title" in website_data
                assert website_data["pricing_info"] == "Freemium"
                assert website_data["contact_info"] == "<EMAIL>"
                assert "twitter" in website_data["social_links"]

    def test_extract_meta_data(self, sample_html_content):
        """Test meta data extraction."""
        engine = SmartResearchEngine()
        soup = BeautifulSoup(sample_html_content, "html.parser")

        meta_data = engine._extract_meta_data(soup)

        assert (
            meta_data["description"] == "A powerful AI tool for automation and analysis"
        )
        assert meta_data["short_description"] == "AI automation made simple"
        assert "ai" in meta_data["tags"]
        assert "automation" in meta_data["tags"]

    def test_extract_social_links(self, sample_html_content):
        """Test social links extraction."""
        engine = SmartResearchEngine()
        soup = BeautifulSoup(sample_html_content, "html.parser")

        social_links = engine._extract_social_links(soup)

        assert "twitter" in social_links
        assert social_links["twitter"] == "https://twitter.com/testaitool"
        assert "github" in social_links
        assert social_links["github"] == "https://github.com/testaitool"

    def test_extract_pricing_info(self, sample_html_content):
        """Test pricing information extraction."""
        engine = SmartResearchEngine()
        soup = BeautifulSoup(sample_html_content, "html.parser")

        pricing_info = engine._extract_pricing_info(soup, sample_html_content)

        assert pricing_info == "Freemium"

    def test_extract_pricing_info_variations(self):
        """Test pricing extraction with different content."""
        engine = SmartResearchEngine()

        # Test free content
        free_html = "<p>This tool is completely free to use</p>"
        soup = BeautifulSoup(free_html, "html.parser")
        pricing = engine._extract_pricing_info(soup, free_html)
        assert pricing == "Free"

        # Test subscription content
        subscription_html = "<p>Monthly subscription starting at $19</p>"
        soup = BeautifulSoup(subscription_html, "html.parser")
        pricing = engine._extract_pricing_info(soup, subscription_html)
        assert pricing == "Subscription"

        # Test paid content
        paid_html = "<p>One-time purchase price of $99</p>"
        soup = BeautifulSoup(paid_html, "html.parser")
        pricing = engine._extract_pricing_info(soup, paid_html)
        assert pricing == "Paid"

    def test_extract_contact_info(self, sample_html_content):
        """Test contact information extraction."""
        engine = SmartResearchEngine()
        soup = BeautifulSoup(sample_html_content, "html.parser")

        contact_info = engine._extract_contact_info(soup)

        assert contact_info == "<EMAIL>"

    def test_combine_research_data(self):
        """Test research data combination."""
        engine = SmartResearchEngine()

        website_data = {
            "title": "Test Tool",
            "features": ["Feature A", "Feature B"],
            "pricing_info": "Free",
        }

        search_data = {
            "description": "Search description",
            "additional_features": ["Feature C", "Feature D"],
            "market_position": "Leading tool",
        }

        combined = engine._combine_research_data(website_data, search_data)

        # Website data should take priority
        assert combined["title"] == "Test Tool"
        assert combined["pricing_info"] == "Free"

        # Search data should be included
        assert combined["description"] == "Search description"
        assert combined["market_position"] == "Leading tool"

        # Features should be merged
        assert len(combined["features"]) == 4
        assert "Feature A" in combined["features"]
        assert "Feature C" in combined["features"]

    @pytest.mark.asyncio
    async def test_search_engines_research(self):
        """Test search engines research (mock implementation)."""
        engine = SmartResearchEngine()

        search_data = await engine._research_search_engines("Test Tool", "tool")

        # Verify mock data structure
        assert "additional_features" in search_data
        assert "market_position" in search_data
        assert "user_reviews" in search_data
        assert "competitors" in search_data

        # Verify entity type is included in responses
        assert "tool" in search_data["additional_features"][0]
        assert "tool" in search_data["market_position"]
