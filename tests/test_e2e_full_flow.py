"""
End-to-end test for the complete pipeline from Product Hunt scraping to database submission.
Tests the entire pipeline flow with mocked external services to ensure data flows correctly
through every component: scraping → classification → research → enhancement → submission.
"""

from datetime import datetime
from unittest.mock import AsyncMock
from uuid import UUID

import pytest

from arep.api.models import Resource, ResourceSubmissionResponse, ToolDetails
from arep.models import ClassificationResult
from arep.pipeline import EnhancementPipeline


@pytest.mark.asyncio
async def test_product_hunt_to_db_flow(mocker):
    """
    Tests the entire pipeline from scraping a local HTML file to the final API submission payload.
    This test simulates the complete pipeline run for a single entity found on the sample HTML page.
    """
    # --- ARRANGE (Setup all our mocks) ---

    # 1. Mock the ProductHuntScraper's collect method directly
    #    This is simpler and more reliable than mocking the HTTP layer
    from datetime import datetime

    from arep.models import MinimalEntity

    mock_entities = [
        MinimalEntity(
            name="Leavoo",
            url="https://www.producthunt.com/products/leavoo",
            logo_url="https://ph-files.imgix.net/db88347c-eb7b-4a27-a70a-4dda72fc6476.x-icon",
            source="product_hunt",
            discovered_at=datetime.utcnow(),
        )
    ]

    # Mock the ProductHuntScraper's collect method to return our test entities
    mocker.patch(
        "arep.collectors.product_hunt.ProductHuntScraper.collect",
        return_value=mock_entities,
    )

    # 2. Mock the research and enhancement steps to avoid external API calls.
    #    We are testing that the pipeline *calls* them, not their internal logic.
    mock_research_data = {
        "summary": "An amazing AI-powered tool for developers.",
        "features": ["Feature A", "Feature B", "AI-driven"],
        "pricing": "Freemium",
        "has_api": True,
        "description": "Time-off made simple, fast & Slack-native",
        "short_description": "Slack-native time-off management tool",
        "social_links": {},
        "pricing_info": "Freemium model available",
        "technical_details": {
            "has_api": True,
            "has_free_tier": True,
            "key_features": ["Slack integration", "Simple time-off", "Fast processing"],
        },
    }

    # Mock the research engine's research method
    mocker.patch(
        "arep.enhancement.research.SmartResearchEngine.research",
        return_value=mock_research_data,
    )

    # Mock the classifier to return a tool classification for our test entity
    mock_classification = ClassificationResult(
        entity_type="tool",
        entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
        confidence=0.95,
        reasoning="Productivity tool with Slack integration detected",
    )

    mocker.patch(
        "arep.classification.classifier.EntityTypeClassifier.classify",
        return_value=mock_classification,
    )

    # Mock the enhancer registry to return a mock tool enhancer
    mock_tool_resource = Resource(
        name="Leavoo",
        website_url="https://www.producthunt.com/products/leavoo",
        short_description="Slack-native time-off management tool",
        entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
        status="PENDING",
        tool_details=ToolDetails(
            key_features=["Slack integration", "Simple time-off", "Fast processing"],
            has_api=True,
            has_free_tier=True,
            pricing_tier="freemium",
        ),
    )

    mock_tool_enhancer = AsyncMock()
    mock_tool_enhancer.enhance.return_value = mock_tool_resource

    mocker.patch(
        "arep.enhancement.registry.enhancer_registry.get_enhancer",
        return_value=mock_tool_enhancer,
    )

    mocker.patch(
        "arep.enhancement.registry.enhancer_registry.has_enhancer", return_value=True
    )

    # 3. Mock the final submission to the AI Navigator API.
    #    This is the most important mock. We want to capture what is sent to it.
    mock_submit_response = ResourceSubmissionResponse(
        success=True,
        message="Resource submitted successfully",
        entity_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
        status="PENDING",
    )

    mock_submitter_instance = mocker.patch(
        "arep.api.client.AINavigatorClient.submit_resource",
        return_value=mock_submit_response,
    )

    # --- ACT (Run the full pipeline) ---

    # Create pipeline with test configuration
    config = {
        "max_concurrent_entities": 1,  # Process one at a time for predictable testing
        "skip_failed_classification": False,
        "skip_unsupported_types": False,
    }
    pipeline = EnhancementPipeline(config=config)

    # Replace the pipeline's collector with a PluggableDataCollector that only uses ProductHuntScraper
    # This makes the test faster and more focused.
    from arep.collectors.collector import PluggableDataCollector
    from arep.collectors.product_hunt import ProductHuntScraper

    # Create a custom collector with only ProductHuntScraper
    custom_collector = PluggableDataCollector()
    custom_collector.collectors = [ProductHuntScraper()]
    pipeline.collector = custom_collector

    # Run the main pipeline logic
    results = await pipeline.run()

    # --- ASSERT (Verify the results) ---

    # 1. Assert that our final submitter was actually called.
    mock_submitter_instance.assert_called()

    # 2. Get the resource that was sent to the submitter.
    #    call_args[0][0] gets the first positional argument of the first call.
    submitted_resource: Resource = mock_submitter_instance.call_args[0][0]

    # 3. Verify the data in the resource is correct.
    #    This proves that data flowed correctly through the entire system.

    # Check data from the scraper (should match the first item in our HTML sample)
    assert submitted_resource.name == "Leavoo"  # First product in our sample HTML
    assert submitted_resource.website_url is not None
    assert "leavoo" in str(submitted_resource.website_url).lower()

    # Check data from the classifier (it should correctly identify it as a tool)
    assert submitted_resource.entity_type_id is not None

    # Check data from the enhancer (using our mocked research data)
    assert "time-off" in submitted_resource.short_description.lower()
    assert isinstance(submitted_resource.tool_details, ToolDetails)
    assert submitted_resource.tool_details.has_api is True
    assert "Slack integration" in submitted_resource.tool_details.key_features

    # Check that the status is set correctly for submission
    assert submitted_resource.status == "PENDING"

    # Verify pipeline results
    assert results["pipeline_completed"] is True
    assert results["entities_processed"] >= 1
    assert results["entities_successful"] >= 1

    print(
        "\n✅ E2E Test Passed: Data flowed correctly from scraping to final submission payload."
    )
    print(
        f"   📊 Pipeline Results: {results['entities_processed']} processed, {results['entities_successful']} successful"
    )
    print(
        f"   🎯 Submitted Resource: {submitted_resource.name} ({submitted_resource.entity_type_id})"
    )
    print(f"   🔗 Website URL: {submitted_resource.website_url}")
    print(f"   📝 Description: {submitted_resource.short_description}")


# TODO: Add a test that validates actual HTML parsing from the saved Product Hunt file
# This would require more sophisticated mocking of aiohttp async context managers
# For now, the test above validates the complete pipeline flow with mocked data
