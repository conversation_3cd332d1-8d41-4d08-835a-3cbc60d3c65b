# AI Resource Enhancement Pipeline - Quick Start Guide

## 🚀 Quick Setup (5 minutes)

### 1. Install & Configure
```bash
# Clone and setup
git clone <repository-url>
cd AI-nav_scrape_2
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# Configure environment
echo "AI_NAV_API_URL=https://ai-nav.onrender.com" > .env
echo "AI_NAV_AUTH_TOKEN=your_token_here" >> .env
```

### 2. Test Installation
```bash
python -m pytest tests/test_integration.py -v
```

### 3. Run Pipeline
```bash
# Dry run (no API submissions)
python run.py --dry-run

# Full pipeline
python run.py
```

## 📋 Common Commands

### Basic Usage
```bash
# Run with custom config
python run.py --config config.json

# Save results to file
python run.py --output results.json

# Debug mode
python run.py --log-level DEBUG

# Help
python run.py --help
```

### Testing
```bash
# All tests
python -m pytest tests/ -v

# Specific component
python -m pytest tests/test_enhancers.py -v

# With coverage
python -m pytest tests/ --cov=arep --cov-report=html
```

## 🔧 Configuration Quick Reference

### Environment Variables
```bash
AI_NAV_API_URL=https://ai-nav.onrender.com
AI_NAV_AUTH_TOKEN=your_auth_token
OPENAI_API_KEY=your_openai_key  # Optional
LOG_LEVEL=INFO
```

### Basic Config File (config.json)
```json
{
  "max_concurrent_entities": 5,
  "skip_failed_classification": true,
  "skip_unsupported_types": true,
  "timeout_seconds": 30
}
```

## 🏗️ Architecture Overview

```
Data Sources → Collection → Classification → Research → Enhancement → API
     ↓             ↓            ↓            ↓           ↓          ↓
  Scrapers    Collector    Classifier   Research    Enhancers   Client
                                       Engine
```

## 📊 Supported Entity Types

| Category | Types |
|----------|-------|
| **Tech** | tool, software, model, platform, hardware |
| **Education** | course, book, research_paper |
| **Business** | agency, service_provider, investor |
| **Community** | content_creator, community, newsletter, podcast |
| **Other** | dataset, project_reference, event, job, grant, bounty, news |

## 🔍 Troubleshooting

### Common Issues

**Classification Failures:**
```bash
# Enable debug logging
python run.py --log-level DEBUG

# Check entity accessibility
curl -I https://example.com
```

**API Errors:**
```bash
# Test API connection
curl -H "Authorization: Bearer $AI_NAV_AUTH_TOKEN" \
     https://ai-nav.onrender.com/api/health

# Reduce rate limiting
python run.py --config '{"max_concurrent_entities": 2}'
```

**Memory Issues:**
```bash
# Reduce concurrency
python run.py --config '{"max_concurrent_entities": 1}'
```

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)

from arep.pipeline import EnhancementPipeline
pipeline = EnhancementPipeline()

# Check status
print(f"Failed entities: {len(pipeline.failed_entities)}")
```

## 🧪 Development

### Add Custom Enhancer
```python
from arep.enhancement.base import BaseEnhancer

class MyEnhancer(BaseEnhancer):
    def __init__(self):
        super().__init__("my_type")

    async def enhance(self, entity, research_data):
        # Your enhancement logic
        pass

# Register
from arep.enhancement.registry import enhancer_registry
enhancer_registry.register_enhancer("my_type", MyEnhancer())
```

### Run Single Entity
```python
import asyncio
from arep.pipeline import EnhancementPipeline
from arep.models import MinimalEntity
from datetime import datetime

async def test_single():
    entity = MinimalEntity(
        name="Test Tool",
        url="https://example.com",
        source="manual",
        discovered_at=datetime.now()
    )

    pipeline = EnhancementPipeline()
    result = await pipeline.process_single_entity(entity)
    print(result)

asyncio.run(test_single())
```

## 📈 Performance Tips

### Optimize for Speed
```json
{
  "max_concurrent_entities": 10,
  "timeout_seconds": 15,
  "skip_failed_classification": true,
  "skip_unsupported_types": true
}
```

### Optimize for Accuracy
```json
{
  "max_concurrent_entities": 3,
  "timeout_seconds": 60,
  "skip_failed_classification": false,
  "enable_llm_classification": true
}
```

### Monitor Performance
```python
# Check processing metrics
results = await pipeline.run()
print(f"Success rate: {results['success_rate']:.1%}")
print(f"Processing time: {results['total_processing_time']:.2f}s")
print(f"Entities/second: {results['entities_processed'] / results['total_processing_time']:.2f}")
```

## 🔗 Useful Links

- **Full Documentation**: [AI-Resource-Enhancement-Pipeline-Documentation.md](./AI-Resource-Enhancement-Pipeline-Documentation.md)
- **Implementation Plan**: [Implementation-Plan:-AI-Resource-Enhancement-Pipeline.md](./Implementation-Plan:-AI-Resource-Enhancement-Pipeline.md)
- **API Reference**: See full documentation
- **Test Examples**: `tests/` directory

## 🆘 Getting Help

1. **Check logs**: `python run.py --log-level DEBUG`
2. **Run tests**: `python -m pytest tests/ -v`
3. **Check documentation**: Full docs in `docs/` directory
4. **Create issue**: Use GitHub issue tracker

## 📝 Quick Examples

### Batch Processing
```python
entities = [
    MinimalEntity(name="Tool 1", url="https://tool1.com", source="manual", discovered_at=datetime.now()),
    MinimalEntity(name="Tool 2", url="https://tool2.com", source="manual", discovered_at=datetime.now()),
]

pipeline = EnhancementPipeline()
results = await pipeline._process_entities_concurrent(entities)
```

### Custom Configuration
```python
config = {
    'max_concurrent_entities': 3,
    'skip_failed_classification': False,
    'timeout_seconds': 45
}

pipeline = EnhancementPipeline(config=config)
results = await pipeline.run()
```

### Error Handling
```python
try:
    results = await pipeline.run()
    print(f"Success: {results['entities_successful']}")
except Exception as e:
    print(f"Pipeline failed: {e}")
```

---

**Need more details?** See the [complete documentation](./AI-Resource-Enhancement-Pipeline-Documentation.md) for comprehensive coverage of all features and capabilities.
