# Live Integration Testing Guide

This guide explains how to run live integration tests that verify real connectivity to your AI Navigator API staging environment.

## Overview

The live integration tests (`tests/test_live_integration.py`) are designed to:

1. **Verify real-world connectivity** to your staging API
2. **Test authentication** with actual credentials
3. **Submit a test entity** to the staging database
4. **Verify the submission** by fetching the created entity
5. **Automatically clean up** the test entity (with manual fallback if needed)

## Prerequisites

### 1. Staging Environment

**⚠️ CRITICAL: Never run these tests against production!**

You must have:
- A staging/development instance of your AI Navigator API
- Valid authentication credentials for the staging environment
- Permission to create and delete test data

### 2. Environment Variables

Set the following environment variables:

```bash
# Required: Staging API URL
export AI_NAV_API_URL="https://your-staging-api.com"

# Required: Valid staging authentication token
export AI_NAV_AUTH_TOKEN="your-staging-auth-token"

# Required: Flag to enable live tests (safety mechanism)
export RUN_LIVE_TESTS="true"
```

## Running the Tests

### Option 1: Using the Helper Script (Recommended)

```bash
# Set your environment variables first
export AI_NAV_API_URL="https://ai-nav-staging.onrender.com"
export AI_NAV_AUTH_TOKEN="your-staging-token"

# Run the helper script
./scripts/run_live_tests.sh
```

The script will:
- Verify your environment variables are set
- Show you what API you're connecting to
- Ask for confirmation before running
- Execute all live integration tests
- Provide cleanup instructions

### Option 2: Using pytest Directly

```bash
# Set environment variables
export AI_NAV_API_URL="https://ai-nav-staging.onrender.com"
export AI_NAV_AUTH_TOKEN="your-staging-token"
export RUN_LIVE_TESTS="true"

# Run the tests
pytest tests/test_live_integration.py -v -s
```

### Option 3: Running Individual Tests

```bash
# Test connectivity only
RUN_LIVE_TESTS=true pytest tests/test_live_integration.py::test_live_api_connectivity -v -s

# Test authentication only
RUN_LIVE_TESTS=true pytest tests/test_live_integration.py::test_live_authentication -v -s

# Test full submission flow
RUN_LIVE_TESTS=true pytest tests/test_live_integration.py::test_live_db_submission -v -s
```

## Test Details

### 1. `test_live_api_connectivity`
- Tests basic connectivity to the staging API
- Calls the health check endpoint
- No data is created or modified

### 2. `test_live_authentication`
- Verifies your authentication token is valid
- Makes a minimal authenticated request
- No data is created or modified

### 3. `test_live_db_submission`
- **Creates real data** in your staging database
- Submits a test entity with name `[TEST] Live Integration Tool`
- Verifies the entity was created successfully
- Fetches the entity to confirm data integrity
- **Automatically deletes** the test entity (with manual fallback if deletion fails)

## Test Entity Details

The test creates an entity with these characteristics:
- **Name**: `[TEST] Live Integration Tool`
- **URL**: `https://example-test.com/live-integration`
- **Type**: Tool (UUID: `a1b2c3d4-e5f6-7890-abcd-123456789012`)
- **Description**: "A temporary tool for live integration testing."
- **Status**: PENDING

## Cleanup Behavior

**✅ Automatic Cleanup**: The tests now automatically attempt to delete test entities after creation.

### Successful Cleanup
When cleanup succeeds, you'll see:
```
✅ Cleanup complete - entity deleted successfully.
```

### Failed Cleanup
If automatic cleanup fails, you'll see:
```
⚠️  Automatic cleanup failed: [error message]
   MANUAL CLEANUP REQUIRED: Entity ID 12345678-1234-1234-1234-123456789012
   Entity name: [TEST] Live Integration Tool
   Created at: 2024-01-15 10:30:45.123456
```

### Manual Cleanup Steps

1. **Log into your staging database admin panel**
2. **Search for entities** with names starting with `[TEST]`
3. **Delete the test entities** created during the test run
4. **Verify deletion** to keep your staging environment clean

### Automatic Cleanup Implementation

The `AINavigatorClient` now includes a `delete_resource` method that automatically cleans up test entities:

```python
async def delete_resource(self, entity_id: str) -> ResourceSubmissionResponse:
    """Delete a resource from the API."""
    logger.info(f"Deleting entity: {entity_id}")

    try:
        response_data = await self._make_request(
            method="DELETE",
            endpoint=f"/entities/{entity_id}",
        )

        return ResourceSubmissionResponse(
            success=True,
            message="Resource deleted successfully",
            data=response_data,
            entity_id=entity_id,
            status="DELETED",
        )
    except APIClientError as e:
        logger.error(f"Failed to delete entity {entity_id}: {e}")
        return ResourceSubmissionResponse(
            success=False,
            message=str(e),
            errors=[APIError(error="delete_failed", message=str(e))],
        )
```

## Safety Mechanisms

### 1. Environment Variable Guard
Tests will be skipped unless `RUN_LIVE_TESTS=true` is explicitly set.

### 2. Test Naming Convention
All test entities are prefixed with `[TEST]` for easy identification.

### 3. Staging URL Verification
The helper script shows you which API URL you're connecting to before running.

### 4. User Confirmation
The helper script requires explicit user confirmation before proceeding.

## Troubleshooting

### Common Issues

1. **Tests are skipped**
   - Ensure `RUN_LIVE_TESTS=true` is set
   - Check that environment variables are exported correctly

2. **Authentication failures**
   - Verify your `AI_NAV_AUTH_TOKEN` is valid for the staging environment
   - Check that the token hasn't expired
   - Ensure you have the correct permissions

3. **Connection errors**
   - Verify your `AI_NAV_API_URL` is correct and accessible
   - Check if the staging environment is running
   - Verify network connectivity

4. **Entity creation failures**
   - Check if the entity type UUID is valid in your staging environment
   - Verify the API schema matches the test payload
   - Review staging database constraints

### Debug Mode

For detailed debugging, run with maximum verbosity:

```bash
RUN_LIVE_TESTS=true pytest tests/test_live_integration.py -v -s --tb=long
```

## Integration with CI/CD

### Recommended Approach

1. **Never run in main CI pipeline** - These tests require staging credentials
2. **Create a separate staging pipeline** with proper secrets management
3. **Run on-demand** or on specific branches/tags
4. **Implement automatic cleanup** before using in automated pipelines

### Example GitHub Actions (Optional)

```yaml
name: Live Integration Tests
on:
  workflow_dispatch:  # Manual trigger only

jobs:
  live-tests:
    runs-on: ubuntu-latest
    environment: staging  # Use GitHub environments for secrets
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run live integration tests
        env:
          AI_NAV_API_URL: ${{ secrets.STAGING_API_URL }}
          AI_NAV_AUTH_TOKEN: ${{ secrets.STAGING_AUTH_TOKEN }}
          RUN_LIVE_TESTS: "true"
        run: pytest tests/test_live_integration.py -v
```

## Next Steps

1. **Run the tests** against your staging environment
2. **Verify all tests pass** and cleanup any test entities
3. **Implement automatic cleanup** by adding a delete method to the API client
4. **Consider adding more test scenarios** (error handling, edge cases, etc.)
5. **Integrate into your deployment workflow** for pre-production validation
