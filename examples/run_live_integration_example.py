#!/usr/bin/env python3
"""
Example script demonstrating how to run live integration tests.

This script shows how to properly set up environment variables and run
the live integration tests against a staging environment.

Usage:
    python examples/run_live_integration_example.py
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """Main function to demonstrate live integration test setup."""

    print("🚀 Live Integration Test Example")
    print("=" * 40)

    # Check if we're in the right directory
    if not Path("tests/test_live_integration.py").exists():
        print("❌ Error: This script must be run from the project root directory")
        print("   Current directory:", os.getcwd())
        print("   Expected to find: tests/test_live_integration.py")
        sys.exit(1)

    # Example environment variables (replace with your actual values)
    example_env = {
        "AI_NAV_API_URL": "https://ai-nav-staging.onrender.com",
        "AI_NAV_AUTH_TOKEN": "your-staging-auth-token-here",
        "RUN_LIVE_TESTS": "true",
    }

    print("📋 Required Environment Variables:")
    print("-" * 40)
    for key, value in example_env.items():
        current_value = os.getenv(key)
        if current_value:
            if key == "AI_NAV_AUTH_TOKEN":
                # Mask the token for security
                display_value = (
                    current_value[:10] + "..." if len(current_value) > 10 else "***"
                )
            else:
                display_value = current_value
            print(f"✅ {key}: {display_value}")
        else:
            print(f"❌ {key}: NOT SET (example: {value})")

    print()

    # Check if all required variables are set
    missing_vars = [key for key in example_env.keys() if not os.getenv(key)]

    if missing_vars:
        print("⚠️  Missing required environment variables:")
        for var in missing_vars:
            print(f"   export {var}='{example_env[var]}'")
        print()
        print("Please set these variables and run the script again.")
        print()
        print("Example setup:")
        print("   export AI_NAV_API_URL='https://your-staging-api.com'")
        print("   export AI_NAV_AUTH_TOKEN='your-staging-token'")
        print("   export RUN_LIVE_TESTS='true'")
        print("   python examples/run_live_integration_example.py")
        return

    # Confirm with user
    print(
        "⚠️  WARNING: This will create and delete test data in your staging environment!"
    )
    print(f"   Target API: {os.getenv('AI_NAV_API_URL')}")
    print()

    response = input("Do you want to proceed? (y/N): ").strip().lower()
    if response not in ["y", "yes"]:
        print("❌ Test run cancelled by user")
        return

    print()
    print("🧪 Running Live Integration Tests...")
    print("=" * 40)

    try:
        # Run the tests using pytest
        cmd = [
            sys.executable,
            "-m",
            "pytest",
            "tests/test_live_integration.py",
            "-v",
            "-s",
            "--tb=short",
        ]

        print(f"Executing: {' '.join(cmd)}")
        print()

        # Run the command with the current environment
        result = subprocess.run(cmd, env=os.environ.copy())

        if result.returncode == 0:
            print()
            print("✅ Live integration tests completed successfully!")
        else:
            print()
            print("❌ Live integration tests failed!")
            print(f"   Exit code: {result.returncode}")

    except FileNotFoundError:
        print("❌ Error: pytest not found. Please install it:")
        print("   pip install pytest")

    except Exception as e:
        print(f"❌ Error running tests: {e}")

    print()
    print("📚 For more information, see:")
    print("   docs/LIVE_INTEGRATION_TESTING.md")


if __name__ == "__main__":
    main()
