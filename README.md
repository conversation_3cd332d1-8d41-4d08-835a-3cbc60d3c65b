# AI Resource Enhancement Pipeline

A comprehensive, production-ready system for discovering, classifying, researching, and enhancing AI-related resources for submission to the AI Navigator platform.

## 🎯 Overview

This pipeline automates the complete process of transforming minimal entity data into rich, structured information:

1. **🔍 Collection** - Discover entities from multiple sources with intelligent deduplication
2. **🏷️ Classification** - Determine entity types using multi-strategy approach with confidence scoring
3. **🔬 Research** - Gather comprehensive data through web scraping and content analysis
4. **⚡ Enhancement** - Transform data using type-specific enhancers for optimal API compatibility
5. **🚀 Submission** - Submit to AI Navigator API with robust error handling and retry logic

## ✨ Key Features

### 🏗️ **Production-Ready Architecture**
- **Concurrent processing** with configurable limits (default: 5 entities simultaneously)
- **Comprehensive error handling** with graceful degradation
- **Real-time monitoring** and metrics collection
- **Configurable behavior** for different deployment scenarios

### 🧠 **Intelligent Processing**
- **Multi-strategy classification** (URL patterns, content analysis, LLM integration)
- **22 entity types supported** with specialized enhancers
- **Confidence scoring** and alternative type suggestions
- **Advanced research engine** with social media detection and pricing analysis

### 🔧 **Developer-Friendly**
- **Extensible architecture** for adding new entity types and data sources
- **Comprehensive testing** with 112+ test cases and 90%+ coverage
- **Detailed documentation** with examples and troubleshooting guides
- **Type-safe implementation** with Pydantic models and async/await

## 🚀 Quick Start

### Installation
```bash
# Clone and setup
git clone <repository-url>
cd AI-nav_scrape_2
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# Configure environment
echo "AI_NAV_API_URL=https://ai-nav.onrender.com" > .env
echo "AI_NAV_AUTH_TOKEN=your_token_here" >> .env
```

### Basic Usage
```bash
# Test installation
python -m pytest tests/test_integration.py -v

# Dry run (no API submissions)
python run.py --dry-run

# Full pipeline
python run.py

# Custom configuration
python run.py --config config.json --log-level DEBUG
```

## 📊 Supported Entity Types

The pipeline supports 22 different AI-related entity types:

| Category | Entity Types |
|----------|-------------|
| **Technology** | tool, software, model, platform, hardware |
| **Education** | course, book, research_paper |
| **Business** | agency, service_provider, investor |
| **Community** | content_creator, community, newsletter, podcast |
| **Data & Projects** | dataset, project_reference |
| **Opportunities** | event, job, grant, bounty |
| **Media** | news |

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Sources  │───▶│   Collection     │───▶│  Classification │
│                 │    │   Pipeline       │    │   Engine        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  AI Navigator   │◀───│   Enhancement    │◀───│   Research      │
│     API         │    │   Pipeline       │    │   Engine        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Core Components

- **`arep/collectors/`** - Multi-source data collection with rate limiting
- **`arep/classification/`** - Intelligent entity type classification
- **`arep/enhancement/`** - Research engine and type-specific enhancers
- **`arep/api/`** - AI Navigator API integration with retry logic
- **`arep/monitoring/`** - Metrics collection and performance monitoring
- **`arep/pipeline.py`** - Main orchestrator managing the complete flow

## 📈 Performance Metrics

- **Processing Speed**: 1-2 seconds per entity (including research)
- **Concurrent Processing**: Configurable (default: 5 entities simultaneously)
- **Success Rate**: 67% in test scenarios (handles failures gracefully)
- **Test Coverage**: 112+ test cases with comprehensive integration testing

## 🔧 Configuration

### Environment Variables
```bash
AI_NAV_API_URL=https://ai-nav.onrender.com
AI_NAV_AUTH_TOKEN=your_auth_token
OPENAI_API_KEY=your_openai_key  # Optional for LLM classification
LOG_LEVEL=INFO
```

### Configuration File (config.json)
```json
{
  "max_concurrent_entities": 5,
  "skip_failed_classification": true,
  "skip_unsupported_types": true,
  "timeout_seconds": 30,
  "enable_llm_classification": false
}
```

## 🧪 Testing

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific component tests
python -m pytest tests/test_enhancers.py -v
python -m pytest tests/test_pipeline.py -v

# Run with coverage
python -m pytest tests/ --cov=arep --cov-report=html

# Integration tests
python -m pytest tests/test_integration.py -v

# End-to-end tests
python -m pytest tests/test_e2e.py -v
```

## 📚 Documentation

- **[Quick Start Guide](docs/Quick-Start-Guide.md)** - Get up and running in 5 minutes
- **[Complete Documentation](docs/AI-Resource-Enhancement-Pipeline-Documentation.md)** - Comprehensive technical documentation
- **[Implementation Plan](docs/Implementation-Plan:-AI-Resource-Enhancement-Pipeline.md)** - Detailed development roadmap
- **[API Documentation](docs/API-docs.md)** - AI Navigator API reference

## 🔍 Example Usage

### Programmatic Usage
```python
import asyncio
from arep.pipeline import EnhancementPipeline
from arep.models import MinimalEntity
from datetime import datetime

async def main():
    # Create pipeline with custom config
    config = {
        'max_concurrent_entities': 3,
        'skip_failed_classification': True
    }
    pipeline = EnhancementPipeline(config=config)

    # Process single entity
    entity = MinimalEntity(
        name="ChatGPT",
        url="https://chat.openai.com",
        source="manual",
        discovered_at=datetime.now()
    )

    result = await pipeline.process_single_entity(entity)
    print(f"Processed: {result['entity_name']}")

    # Or run complete pipeline
    results = await pipeline.run()
    print(f"Success rate: {results['success_rate']:.1%}")

asyncio.run(main())
```

### Custom Enhancer Development
```python
from arep.enhancement.base import BaseEnhancer
from arep.enhancement.registry import enhancer_registry

class CustomEnhancer(BaseEnhancer):
    def __init__(self):
        super().__init__("custom_type")

    async def enhance(self, entity, research_data):
        # Custom enhancement logic
        base_fields = self._extract_base_fields(entity, research_data)
        return Resource(**base_fields)

# Register custom enhancer
enhancer_registry.register_enhancer("custom_type", CustomEnhancer())
```

## 🚀 Deployment

### Docker
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "run.py"]
```

### Production Configuration
```json
{
  "max_concurrent_entities": 10,
  "timeout_seconds": 45,
  "enable_monitoring": true,
  "api_rate_limit": 8,
  "cache_results": true
}
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/new-enhancer`
3. **Make changes** with comprehensive tests
4. **Run tests**: `python -m pytest tests/ -v`
5. **Submit pull request**

See [Contributing Guidelines](docs/AI-Resource-Enhancement-Pipeline-Documentation.md#contributing) for detailed information.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: [Complete Documentation](docs/AI-Resource-Enhancement-Pipeline-Documentation.md)
- **Quick Help**: [Quick Start Guide](docs/Quick-Start-Guide.md)
- **Issues**: GitHub issue tracker
- **Debug**: `python run.py --log-level DEBUG`

---

**Built with ❤️ for the AI community** - Transforming minimal data into rich, structured AI resource information.
