"""
Course-specific enhancer for AI Resource Enhancement Pipeline.
Transforms research data into course-specific API format.
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from arep.api.models import CourseDetails, Resource
from arep.enhancement.base import BaseEnhancer, EnhancementError
from arep.models import ClassifiedEntity, ResearchData
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class CourseEnhancer(BaseEnhancer):
    """
    Enhancer specifically for courses and educational content.

    Transforms research data into CourseDetails format with:
    - Instructor information
    - Duration and skill level
    - Prerequisites
    - Certificate availability
    - Enrollment details
    """

    def __init__(self):
        super().__init__("course")

    async def enhance(
        self, entity: ClassifiedEntity, research_data: ResearchData
    ) -> Resource:
        """
        Transform classified entity and research data into course-specific Resource.

        Args:
            entity: Classified entity with basic information
            research_data: Research data gathered about the entity

        Returns:
            Resource object with CourseDetails ready for API submission
        """
        self.logger.info(f"Enhancing course entity: {entity.name}")

        try:
            # Extract base fields common to all entities
            base_fields = self._extract_base_fields(entity, research_data)

            # Create course-specific details
            course_details = await self._create_course_details(entity, research_data)

            # Validate required fields
            required_fields = ["name", "website_url", "entity_type_id"]
            if not self._validate_required_fields(base_fields, required_fields):
                raise EnhancementError(
                    f"Missing required fields for course: {entity.name}",
                    entity_name=entity.name,
                    entity_type="course",
                )

            # Create Resource object
            resource = Resource(**base_fields, course_details=course_details)

            self.logger.info(f"Successfully enhanced course: {entity.name}")
            return resource

        except Exception as e:
            self.logger.error(f"Failed to enhance course {entity.name}: {e}")
            raise EnhancementError(
                f"Course enhancement failed: {e}",
                entity_name=entity.name,
                entity_type="course",
            )

    async def _create_course_details(
        self, entity: ClassifiedEntity, research_data: ResearchData
    ) -> CourseDetails:
        """
        Create CourseDetails object from research data.

        Args:
            entity: Classified entity
            research_data: Research data

        Returns:
            CourseDetails object
        """
        # Extract instructor information
        instructor_name = self._extract_instructor_name(research_data)

        # Extract duration
        duration_text = self._extract_duration(research_data)

        # Determine skill level
        skill_level = self._determine_skill_level(research_data)

        # Extract prerequisites
        prerequisites = self._extract_prerequisites(research_data)

        # Check certificate availability
        certificate_available = self._detect_certificate_availability(research_data)

        # Extract enrollment count if available
        enrollment_count = self._extract_enrollment_count(research_data)

        course_details = CourseDetails(
            instructor_name=instructor_name,
            duration_text=duration_text,
            skill_level=skill_level,
            prerequisites=prerequisites,
            certificate_available=certificate_available,
            enrollment_count=enrollment_count,
        )

        return course_details

    def _extract_instructor_name(self, research_data: ResearchData) -> Optional[str]:
        """Extract instructor name from research data."""
        # Look for instructor patterns in description
        if research_data.description:
            instructor_patterns = [
                r"(?:instructor|teacher|taught by|by|created by)[:\s]+([A-Z][a-z]+ [A-Z][a-z]+)",
                r"(?:Dr\.|Prof\.|Mr\.|Ms\.)\s+([A-Z][a-z]+ [A-Z][a-z]+)",
                r"([A-Z][a-z]+ [A-Z][a-z]+)(?:\s+(?:teaches|instructs|leads))",
            ]

            for pattern in instructor_patterns:
                match = re.search(pattern, research_data.description, re.IGNORECASE)
                if match:
                    return match.group(1).strip()

        # Check technical details
        if research_data.technical_details and isinstance(
            research_data.technical_details, dict
        ):
            instructor = research_data.technical_details.get("instructor")
            if instructor:
                return str(instructor)

        return None

    def _extract_duration(self, research_data: ResearchData) -> Optional[str]:
        """Extract course duration from research data."""
        if research_data.description:
            # Duration patterns
            duration_patterns = [
                r"(\d+)\s*(?:hours?|hrs?)",
                r"(\d+)\s*(?:weeks?|wks?)",
                r"(\d+)\s*(?:months?|mos?)",
                r"(\d+)\s*(?:days?)",
                r"(\d+)\s*(?:minutes?|mins?)",
                r"(\d+[-–]\d+)\s*(?:hours?|hrs?)",
                r"(\d+[-–]\d+)\s*(?:weeks?|wks?)",
            ]

            for pattern in duration_patterns:
                match = re.search(pattern, research_data.description, re.IGNORECASE)
                if match:
                    duration_value = match.group(1)
                    # Determine unit from the full match
                    full_match = match.group(0).lower()
                    if "hour" in full_match or "hr" in full_match:
                        return f"{duration_value} hours"
                    elif "week" in full_match or "wk" in full_match:
                        return f"{duration_value} weeks"
                    elif "month" in full_match or "mo" in full_match:
                        return f"{duration_value} months"
                    elif "day" in full_match:
                        return f"{duration_value} days"
                    elif "minute" in full_match or "min" in full_match:
                        return f"{duration_value} minutes"

        return None

    def _determine_skill_level(self, research_data: ResearchData) -> Optional[str]:
        """Determine skill level from research data."""
        if research_data.description:
            desc_lower = research_data.description.lower()

            # Skill level keywords
            if any(
                term in desc_lower
                for term in [
                    "beginner",
                    "basic",
                    "intro",
                    "fundamentals",
                    "getting started",
                ]
            ):
                return "Beginner"
            elif any(
                term in desc_lower
                for term in ["intermediate", "moderate", "some experience"]
            ):
                return "Intermediate"
            elif any(
                term in desc_lower
                for term in ["advanced", "expert", "professional", "mastery"]
            ):
                return "Advanced"
            elif any(
                term in desc_lower for term in ["all levels", "any level", "everyone"]
            ):
                return "All Levels"

        # Check prerequisites to infer level
        if research_data.technical_details:
            tech_text = str(research_data.technical_details).lower()
            if any(
                term in tech_text
                for term in ["no experience", "no prior", "no background"]
            ):
                return "Beginner"
            elif any(
                term in tech_text for term in ["some experience", "basic knowledge"]
            ):
                return "Intermediate"
            elif any(
                term in tech_text for term in ["extensive experience", "deep knowledge"]
            ):
                return "Advanced"

        return "Beginner"  # Default to beginner

    def _extract_prerequisites(self, research_data: ResearchData) -> Optional[str]:
        """Extract prerequisites from research data."""
        if research_data.description:
            # Look for prerequisite patterns
            prereq_patterns = [
                r"(?:prerequisites?|requirements?|you should)[:\s]+([^.!?]+)",
                r"(?:before taking|prior to)[:\s]+([^.!?]+)",
                r"(?:assumes?|requires?)[:\s]+([^.!?]+)",
            ]

            for pattern in prereq_patterns:
                match = re.search(pattern, research_data.description, re.IGNORECASE)
                if match:
                    prereq_text = match.group(1).strip()
                    if len(prereq_text) > 10:  # Ensure it's substantial
                        return self._clean_text_field(prereq_text, max_length=200)

        # Check technical details
        if research_data.technical_details and isinstance(
            research_data.technical_details, dict
        ):
            prereqs = research_data.technical_details.get("prerequisites")
            if prereqs:
                return self._clean_text_field(str(prereqs), max_length=200)

        return None

    def _detect_certificate_availability(
        self, research_data: ResearchData
    ) -> Optional[bool]:
        """Detect if course offers a certificate."""
        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.features:
            search_text += " ".join(research_data.features).lower()

        # Certificate keywords
        cert_keywords = [
            "certificate",
            "certification",
            "credential",
            "diploma",
            "completion certificate",
            "course certificate",
        ]

        if any(keyword in search_text for keyword in cert_keywords):
            return True

        # Check for negative indicators
        no_cert_keywords = ["no certificate", "without certificate"]
        if any(keyword in search_text for keyword in no_cert_keywords):
            return False

        return None  # Unknown

    def _extract_enrollment_count(self, research_data: ResearchData) -> Optional[int]:
        """Extract enrollment count from research data."""
        if research_data.description:
            # Look for enrollment numbers
            enrollment_patterns = [
                r"(\d+(?:,\d+)*)\s*(?:students?|learners?|enrolled)",
                r"(?:over|more than)\s*(\d+(?:,\d+)*)\s*(?:students?|learners?)",
                r"(\d+(?:,\d+)*)\+?\s*(?:students?|learners?)",
            ]

            for pattern in enrollment_patterns:
                match = re.search(pattern, research_data.description, re.IGNORECASE)
                if match:
                    count_str = match.group(1).replace(",", "")
                    try:
                        return int(count_str)
                    except ValueError:
                        continue

        return None
