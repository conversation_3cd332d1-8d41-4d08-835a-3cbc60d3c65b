"""
Tool-specific enhancer for AI Resource Enhancement Pipeline.
Transforms research data into tool-specific API format.
"""

import re
from typing import Any, Dict, List, Optional

from arep.api.models import Resource, ToolDetails
from arep.enhancement.base import BaseEnhancer, EnhancementError
from arep.models import ClassifiedEntity, ResearchData
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class ToolEnhancer(BaseEnhancer):
    """
    Enhancer specifically for AI tools and software.

    Transforms research data into ToolDetails format with:
    - Key features extraction
    - API availability detection
    - Free tier analysis
    - Use case identification
    - Integration capabilities
    """

    def __init__(self):
        super().__init__("tool")

    async def enhance(
        self, entity: ClassifiedEntity, research_data: ResearchData
    ) -> Resource:
        """
        Transform classified entity and research data into tool-specific Resource.

        Args:
            entity: Classified entity with basic information
            research_data: Research data gathered about the entity

        Returns:
            Resource object with ToolDetails ready for API submission
        """
        self.logger.info(f"Enhancing tool entity: {entity.name}")

        try:
            # Extract base fields common to all entities
            base_fields = self._extract_base_fields(entity, research_data)

            # Create tool-specific details
            tool_details = await self._create_tool_details(entity, research_data)

            # Validate required fields
            required_fields = ["name", "website_url", "entity_type_id"]
            if not self._validate_required_fields(base_fields, required_fields):
                raise EnhancementError(
                    f"Missing required fields for tool: {entity.name}",
                    entity_name=entity.name,
                    entity_type="tool",
                )

            # Create Resource object
            resource = Resource(**base_fields, tool_details=tool_details)

            self.logger.info(f"Successfully enhanced tool: {entity.name}")
            return resource

        except Exception as e:
            self.logger.error(f"Failed to enhance tool {entity.name}: {e}")
            raise EnhancementError(
                f"Tool enhancement failed: {e}",
                entity_name=entity.name,
                entity_type="tool",
            )

    async def _create_tool_details(
        self, entity: ClassifiedEntity, research_data: ResearchData
    ) -> ToolDetails:
        """
        Create ToolDetails object from research data.

        Args:
            entity: Classified entity
            research_data: Research data

        Returns:
            ToolDetails object
        """
        # Extract key features
        key_features = self._extract_key_features(research_data)

        # Detect API availability
        has_api = self._detect_api_availability(research_data)

        # Determine free tier availability
        has_free_tier = self._detect_free_tier(research_data)

        # Extract use cases
        use_cases = self._extract_use_cases(research_data)

        # Detect integration capabilities
        integrations = self._detect_integrations(research_data)

        # Extract supported platforms
        supported_platforms = self._extract_platforms(research_data)

        # Determine deployment options
        deployment_options = self._extract_deployment_options(research_data)

        tool_details = ToolDetails(
            key_features=key_features,
            has_api=has_api,
            has_free_tier=has_free_tier,
            use_cases=use_cases,
            integrations_available=integrations,
            supported_platforms=supported_platforms,
            deployment_options=deployment_options,
        )

        return tool_details

    def _extract_key_features(self, research_data: ResearchData) -> List[str]:
        """Extract key features from research data."""
        features = self._extract_features_list(research_data, max_features=8)

        # If no features found, try to extract from description
        if not features and research_data.description:
            features = self._extract_features_from_text(research_data.description)

        # Ensure we have at least some features
        if not features:
            features = ["AI-powered capabilities", "User-friendly interface"]

        return features

    def _extract_features_from_text(self, text: str) -> List[str]:
        """Extract features from descriptive text using patterns."""
        features = []

        # Common feature patterns
        feature_patterns = [
            r"(?:features?|capabilities?|offers?|provides?|includes?)[:\s]+([^.!?]+)",
            r"(?:can|able to|allows?)[:\s]+([^.!?]+)",
            r"(?:supports?|enables?)[:\s]+([^.!?]+)",
        ]

        for pattern in feature_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                # Clean and split the match
                clean_features = [f.strip() for f in match.split(",") if f.strip()]
                features.extend(clean_features[:3])  # Limit per pattern

        return features[:5]  # Return top 5 features

    def _detect_api_availability(self, research_data: ResearchData) -> bool:
        """Detect if the tool has an API."""
        # Check in features
        if research_data.features:
            for feature in research_data.features:
                if any(
                    term in feature.lower()
                    for term in ["api", "rest", "graphql", "webhook"]
                ):
                    return True

        # Check in description
        if research_data.description:
            api_terms = [
                "api",
                "rest api",
                "graphql",
                "webhook",
                "integration",
                "developer",
            ]
            if any(term in research_data.description.lower() for term in api_terms):
                return True

        # Check in technical details
        if research_data.technical_details:
            tech_text = str(research_data.technical_details).lower()
            if any(term in tech_text for term in ["api", "rest", "graphql"]):
                return True

        return False

    def _detect_free_tier(self, research_data: ResearchData) -> bool:
        """Detect if the tool has a free tier."""
        pricing_tier = self._determine_pricing_tier(research_data)
        return pricing_tier in ["free", "freemium"]

    def _extract_use_cases(self, research_data: ResearchData) -> List[str]:
        """Extract use cases from research data."""
        use_cases = []

        # Common AI tool use cases
        common_use_cases = [
            "content creation",
            "data analysis",
            "automation",
            "machine learning",
            "natural language processing",
            "image processing",
            "chatbots",
            "recommendation systems",
            "predictive analytics",
            "workflow optimization",
        ]

        # Check description for use cases
        if research_data.description:
            desc_lower = research_data.description.lower()
            for use_case in common_use_cases:
                if use_case in desc_lower:
                    use_cases.append(use_case.title())

        # If no specific use cases found, infer from entity type and features
        if not use_cases:
            use_cases = ["AI automation", "Business intelligence"]

        return use_cases[:5]  # Limit to 5 use cases

    def _detect_integrations(self, research_data: ResearchData) -> List[str]:
        """Detect available integrations."""
        integrations = []

        # Common integration platforms
        integration_platforms = [
            "slack",
            "discord",
            "teams",
            "zapier",
            "salesforce",
            "hubspot",
            "google",
            "microsoft",
            "aws",
            "azure",
            "github",
            "jira",
        ]

        # Check description and features for integrations
        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.features:
            search_text += " ".join(research_data.features).lower()

        for platform in integration_platforms:
            if platform in search_text:
                integrations.append(platform.title())

        return integrations[:8]  # Limit to 8 integrations

    def _extract_platforms(self, research_data: ResearchData) -> List[str]:
        """Extract supported platforms."""
        platforms = []

        # Common platforms
        platform_keywords = {
            "web": ["web", "browser", "online"],
            "mobile": ["mobile", "ios", "android", "app"],
            "desktop": ["desktop", "windows", "mac", "linux"],
            "cloud": ["cloud", "saas", "hosted"],
        }

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.technical_details:
            search_text += str(research_data.technical_details).lower()

        for platform, keywords in platform_keywords.items():
            if any(keyword in search_text for keyword in keywords):
                platforms.append(platform.title())

        # Default to web if no platforms detected
        if not platforms:
            platforms = ["Web"]

        return platforms

    def _extract_deployment_options(self, research_data: ResearchData) -> List[str]:
        """Extract deployment options."""
        deployment_options = []

        # Common deployment patterns
        deployment_keywords = {
            "cloud": ["cloud", "saas", "hosted", "online"],
            "on-premise": ["on-premise", "self-hosted", "private"],
            "hybrid": ["hybrid", "multi-cloud"],
            "api": ["api", "service", "endpoint"],
        }

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.technical_details:
            search_text += str(research_data.technical_details).lower()

        for deployment, keywords in deployment_keywords.items():
            if any(keyword in search_text for keyword in keywords):
                deployment_options.append(deployment.replace("-", " ").title())

        # Default deployment option
        if not deployment_options:
            deployment_options = ["Cloud"]

        return deployment_options
