"""
Smart Research Engine for AI Resource Enhancement Pipeline.
Researches entities to gather comprehensive data for enhancement.
"""

import asyncio
import re
import time
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin, urlparse

import aiohttp
from bs4 import BeautifulSoup

from arep.models import ClassifiedEntity, ResearchData
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class SmartResearchEngine:
    """
    Intelligent research engine that gathers comprehensive data about entities.

    Features:
    - Web content analysis
    - Search API integration (extensible)
    - Social media data extraction
    - Technical specification detection
    - Pricing and feature analysis
    """

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                "User-Agent": "Mozilla/5.0 (compatible; AI-Resource-Enhancement-Pipeline/1.0)"
            },
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def research(self, entity: ClassifiedEntity) -> ResearchData:
        """
        Conduct comprehensive research on an entity.

        Args:
            entity: Classified entity to research

        Returns:
            ResearchData with comprehensive information
        """
        logger.info(f"Starting research for entity: {entity.name}")
        start_time = time.time()

        research_sources = []

        try:
            # Primary research: scrape the entity's website
            website_data = await self._research_website(entity.url, entity.name)
            research_sources.append(str(entity.url))

            # Secondary research: search for additional information
            search_data = await self._research_search_engines(
                entity.name, entity.entity_type
            )

            # Combine and analyze all data
            combined_data = self._combine_research_data(website_data, search_data)

            # Create research data object
            research_data = ResearchData(
                description=combined_data.get("description"),
                short_description=combined_data.get("short_description"),
                features=combined_data.get("features", []),
                categories=combined_data.get("categories", []),
                tags=combined_data.get("tags", []),
                pricing_info=combined_data.get("pricing_info"),
                contact_info=combined_data.get("contact_info"),
                social_links=combined_data.get("social_links"),
                technical_details=combined_data.get("technical_details"),
                research_sources=research_sources,
                research_timestamp=datetime.now(),
            )

            processing_time = time.time() - start_time
            logger.info(
                f"Research completed for {entity.name} in {processing_time:.2f}s"
            )

            return research_data

        except Exception as e:
            logger.error(f"Research failed for {entity.name}: {e}")
            # Return minimal research data on failure
            return ResearchData(
                description=f"Research failed for {entity.name}",
                short_description=f"Research failed for {entity.name}",
                research_sources=research_sources,
                research_timestamp=datetime.now(),
            )

    async def _research_website(self, url: str, entity_name: str) -> Dict[str, Any]:
        """Research the entity's primary website."""
        logger.debug(f"Researching website: {url}")

        try:
            if not self.session:
                raise ValueError("Session not initialized. Use async context manager.")

            async with self.session.get(str(url)) as response:
                if response.status != 200:
                    logger.warning(f"Failed to fetch {url}: HTTP {response.status}")
                    return {}

                html_content = await response.text()

            # Extract structured data from HTML using BeautifulSoup
            soup = BeautifulSoup(html_content, "html.parser")
            content_data = self._extract_content_from_soup(soup, str(url))

            # Extract additional website-specific data
            soup = BeautifulSoup(html_content, "html.parser")

            # Extract meta information
            meta_data = self._extract_meta_data(soup)

            # Extract social links
            social_links = self._extract_social_links(soup)

            # Extract pricing information
            pricing_info = self._extract_pricing_info(soup, html_content)

            # Extract contact information
            contact_info = self._extract_contact_info(soup)

            # Combine all data
            website_data = {
                **content_data,
                **meta_data,
                "social_links": social_links,
                "pricing_info": pricing_info,
                "contact_info": contact_info,
            }

            return website_data

        except Exception as e:
            logger.error(f"Website research failed for {url}: {e}")
            return {}

    async def _research_search_engines(
        self, entity_name: str, entity_type: str
    ) -> Dict[str, Any]:
        """Research using search engines (mock implementation)."""
        logger.debug(f"Searching for additional info about: {entity_name}")

        # Mock implementation - in production, this would use:
        # - Perplexity API
        # - Brave Search API
        # - Google Custom Search API
        # - Bing Search API

        await asyncio.sleep(0.1)  # Simulate API call

        return {
            "additional_features": [f"Advanced {entity_type} capabilities"],
            "market_position": f"Leading {entity_type} in the market",
            "user_reviews": "Generally positive user feedback",
            "competitors": [f"Similar {entity_type} tools"],
        }

    def _combine_research_data(
        self, website_data: Dict[str, Any], search_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Combine and prioritize data from multiple research sources."""
        combined = {}

        # Prioritize website data over search data
        combined.update(search_data)
        combined.update(website_data)

        # Merge lists intelligently
        if "features" in website_data and "additional_features" in search_data:
            all_features = website_data.get("features", []) + search_data.get(
                "additional_features", []
            )
            combined["features"] = list(set(all_features))  # Remove duplicates

        return combined

    def _extract_meta_data(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract meta information from HTML."""
        meta_data = {}

        # Extract meta description
        meta_desc = soup.find("meta", attrs={"name": "description"})
        if meta_desc and meta_desc.get("content"):
            meta_data["description"] = meta_desc["content"].strip()

        # Extract Open Graph data
        og_desc = soup.find("meta", attrs={"property": "og:description"})
        if og_desc and og_desc.get("content"):
            meta_data["short_description"] = og_desc["content"].strip()

        # Extract keywords
        keywords_meta = soup.find("meta", attrs={"name": "keywords"})
        if keywords_meta and keywords_meta.get("content"):
            keywords = [k.strip() for k in keywords_meta["content"].split(",")]
            meta_data["tags"] = keywords

        return meta_data

    def _extract_social_links(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract social media links from HTML."""
        social_links = {}

        # Common social media patterns
        social_patterns = {
            "twitter": ["twitter.com", "t.co"],
            "linkedin": ["linkedin.com"],
            "github": ["github.com"],
            "youtube": ["youtube.com", "youtu.be"],
            "facebook": ["facebook.com", "fb.com"],
            "instagram": ["instagram.com"],
        }

        # Find all links
        links = soup.find_all("a", href=True)

        for link in links:
            href = link["href"].lower()
            for platform, domains in social_patterns.items():
                if any(domain in href for domain in domains):
                    social_links[platform] = link["href"]
                    break

        return social_links

    def _extract_pricing_info(
        self, soup: BeautifulSoup, html_content: str
    ) -> Optional[str]:
        """Extract pricing information from HTML."""
        pricing_keywords = [
            "pricing",
            "price",
            "cost",
            "free",
            "premium",
            "subscription",
            "plan",
            "tier",
            "freemium",
            "trial",
            "billing",
        ]

        # Look for pricing-related text
        text_content = html_content.lower()

        if "free" in text_content and (
            "trial" in text_content or "tier" in text_content
        ):
            return "Freemium"
        elif "free" in text_content:
            return "Free"
        elif any(
            keyword in text_content for keyword in ["subscription", "monthly", "yearly"]
        ):
            return "Subscription"
        elif any(keyword in text_content for keyword in ["pricing", "price", "cost"]):
            return "Paid"

        return None

    def _extract_contact_info(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract contact information from HTML."""
        # Look for contact links
        contact_links = soup.find_all("a", href=True)

        for link in contact_links:
            href = link["href"].lower()
            text = link.get_text().lower()

            if "mailto:" in href:
                return href.replace("mailto:", "")
            elif any(word in text for word in ["contact", "support", "help"]):
                return link["href"]

        return None

    def _extract_content_from_soup(
        self, soup: BeautifulSoup, url: str
    ) -> Dict[str, Any]:
        """Extract content data from BeautifulSoup object."""
        return {
            "title": self._extract_title_from_soup(soup),
            "description": self._extract_description_from_soup(soup),
            "features": self._extract_features_from_soup(soup),
            "content_text": soup.get_text(),
        }

    def _extract_title_from_soup(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract title from soup."""
        title_tag = soup.find("title")
        if title_tag:
            return title_tag.get_text().strip()

        h1_tag = soup.find("h1")
        if h1_tag:
            return h1_tag.get_text().strip()

        return None

    def _extract_description_from_soup(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract description from soup."""
        desc_tag = soup.find("meta", attrs={"name": "description"})
        if desc_tag and desc_tag.get("content"):
            return desc_tag["content"].strip()

        og_desc = soup.find("meta", attrs={"property": "og:description"})
        if og_desc and og_desc.get("content"):
            return og_desc["content"].strip()

        return None

    def _extract_features_from_soup(self, soup: BeautifulSoup) -> List[str]:
        """Extract features from soup."""
        features = []

        # Look for feature lists
        feature_sections = soup.find_all(
            ["ul", "ol"], class_=re.compile(r"feature|benefit|capability")
        )
        for section in feature_sections:
            items = section.find_all("li")
            for item in items[:5]:  # Limit to 5 features per section
                text = item.get_text().strip()
                if text and len(text) > 5:
                    features.append(text)

        return features[:10]  # Limit total features
