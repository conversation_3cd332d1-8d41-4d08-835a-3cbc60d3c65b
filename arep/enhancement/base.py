"""
Base enhancer class for AI Resource Enhancement Pipeline.
Provides abstract interface for type-specific entity enhancers.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from uuid import UUID

from arep.api.models import Resource
from arep.models import ClassifiedEntity, ResearchData
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class BaseEnhancer(ABC):
    """
    Abstract base class for entity type-specific enhancers.

    Each entity type (tool, course, agency, etc.) should have its own enhancer
    that inherits from this class and implements the enhance method.
    """

    def __init__(self, entity_type: str):
        """
        Initialize the enhancer.

        Args:
            entity_type: The entity type this enhancer handles
        """
        self.entity_type = entity_type
        self.logger = get_logger(f"{__name__}.{entity_type}")

    @abstractmethod
    async def enhance(
        self, entity: ClassifiedEntity, research_data: ResearchData
    ) -> Resource:
        """
        Transform classified entity and research data into API-ready Resource.

        Args:
            entity: Classified entity with basic information
            research_data: Research data gathered about the entity

        Returns:
            Resource object ready for API submission

        Raises:
            EnhancementError: If enhancement fails
        """
        pass

    def _extract_base_fields(
        self, entity: ClassifiedEntity, research_data: ResearchData
    ) -> Dict[str, Any]:
        """
        Extract common fields that apply to all entity types.

        Args:
            entity: Classified entity
            research_data: Research data

        Returns:
            Dictionary of base fields for Resource creation
        """
        base_fields = {
            "name": entity.name,
            "website_url": entity.url,
            "entity_type_id": entity.entity_type_id,
            "short_description": research_data.short_description
            or f"AI resource: {entity.name}",
            "description": research_data.description,
            "logo_url": entity.logo_url,
        }

        # Add optional fields if available
        if research_data.contact_info:
            # Only add contact_url if it's a valid URL, not an email
            contact = research_data.contact_info
            if contact.startswith("http://") or contact.startswith("https://"):
                base_fields["contact_url"] = contact

        if research_data.social_links:
            base_fields["social_links"] = research_data.social_links

        # Extract categories and tags if available
        if research_data.categories:
            # Note: In production, these would need to be mapped to actual category UUIDs
            base_fields["category_names"] = research_data.categories

        if research_data.tags:
            # Note: In production, these would need to be mapped to actual tag UUIDs
            base_fields["tag_names"] = research_data.tags

        return base_fields

    def _validate_required_fields(
        self, resource_data: Dict[str, Any], required_fields: list
    ) -> bool:
        """
        Validate that all required fields are present and non-empty.

        Args:
            resource_data: Resource data dictionary
            required_fields: List of required field names

        Returns:
            True if all required fields are present, False otherwise
        """
        for field in required_fields:
            if field not in resource_data or not resource_data[field]:
                self.logger.warning(f"Missing required field: {field}")
                return False
        return True

    def _clean_text_field(
        self, text: Optional[str], max_length: Optional[int] = None
    ) -> Optional[str]:
        """
        Clean and validate text fields.

        Args:
            text: Text to clean
            max_length: Maximum allowed length

        Returns:
            Cleaned text or None
        """
        if not text:
            return None

        # Clean whitespace
        cleaned = text.strip()

        # Truncate if necessary
        if max_length and len(cleaned) > max_length:
            cleaned = cleaned[: max_length - 3] + "..."

        return cleaned if cleaned else None

    def _extract_features_list(
        self, research_data: ResearchData, max_features: int = 10
    ) -> list:
        """
        Extract and clean features list from research data.

        Args:
            research_data: Research data
            max_features: Maximum number of features to include

        Returns:
            List of cleaned feature strings
        """
        features = research_data.features or []

        # Clean and filter features
        cleaned_features = []
        for feature in features[:max_features]:
            if isinstance(feature, str) and feature.strip():
                cleaned_features.append(feature.strip())

        return cleaned_features

    def _determine_pricing_tier(self, research_data: ResearchData) -> Optional[str]:
        """
        Determine pricing tier from research data.

        Args:
            research_data: Research data

        Returns:
            Pricing tier string or None
        """
        pricing_info = research_data.pricing_info
        if not pricing_info:
            return None

        pricing_lower = pricing_info.lower()

        if "free" in pricing_lower and (
            "trial" in pricing_lower or "tier" in pricing_lower
        ):
            return "freemium"
        elif "free" in pricing_lower:
            return "free"
        elif any(
            term in pricing_lower for term in ["subscription", "monthly", "yearly"]
        ):
            return "subscription"
        elif any(term in pricing_lower for term in ["paid", "premium", "pro"]):
            return "paid"

        return None


class EnhancementError(Exception):
    """Exception raised when entity enhancement fails."""

    def __init__(self, message: str, entity_name: str = None, entity_type: str = None):
        self.entity_name = entity_name
        self.entity_type = entity_type
        super().__init__(message)
