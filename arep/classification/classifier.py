"""
Entity type classification system for the AI Resource Enhancement Pipeline.
Classifies entities into appropriate types based on various signals and content analysis.
"""

import re
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse

import aiohttp
from bs4 import BeautifulSoup

from arep.models import ClassificationResult, MinimalEntity
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class BaseClassifier(ABC):
    """
    Abstract base class for entity classifiers.
    Defines the interface that all classifiers must implement.
    """
    
    def __init__(self, name: str):
        """
        Initialize the classifier.
        
        Args:
            name: Name of the classifier
        """
        self.name = name
    
    @abstractmethod
    async def classify(self, entity: MinimalEntity) -> ClassificationResult:
        """
        Classify an entity.
        
        Args:
            entity: Entity to classify
            
        Returns:
            Classification result
        """
        pass
    
    def get_confidence_threshold(self) -> float:
        """Get the minimum confidence threshold for this classifier."""
        return 0.5


class URLPatternClassifier(BaseClassifier):
    """
    Classifier that uses URL patterns to determine entity type.
    Fast and lightweight classification based on domain patterns.
    """
    
    def __init__(self):
        super().__init__("url_pattern")
        
        # Define URL patterns for different entity types
        self.patterns = {
            "tool": [
                r"\.ai$", r"\.ml$", r"ai\.", r"ml\.", r"gpt", r"chat", r"bot",
                r"api", r"platform", r"app", r"tool", r"studio", r"lab",
                r"prompt", r"prompts", r"promptbase", r"prompthero", r"promptlayer"
            ],
            "course": [
                r"course", r"learn", r"education", r"training", r"tutorial",
                r"academy", r"school", r"university", r"udemy", r"coursera"
            ],
            "agency": [
                r"agency", r"consulting", r"services", r"solutions", r"studio",
                r"digital", r"marketing", r"design"
            ],
            "content_creator": [
                r"blog", r"medium\.com", r"substack", r"youtube", r"twitter",
                r"linkedin", r"personal", r"portfolio"
            ],
            "community": [
                r"discord", r"slack", r"reddit", r"forum", r"community",
                r"group", r"club", r"society"
            ],
            "newsletter": [
                r"newsletter", r"substack", r"mailchimp", r"beehiiv",
                r"convertkit", r"weekly", r"daily"
            ],
            "dataset": [
                r"dataset", r"data", r"kaggle", r"huggingface\.co/datasets",
                r"github\.com.*dataset", r"csv", r"json"
            ],
            "research_paper": [
                r"arxiv", r"paper", r"research", r"academic", r"scholar",
                r"ieee", r"acm", r"nature", r"science"
            ],
            "software": [
                r"github\.com", r"gitlab", r"bitbucket", r"software",
                r"library", r"framework", r"package", r"npm", r"pypi"
            ],
            "model": [
                r"huggingface\.co/models", r"model", r"checkpoint",
                r"weights", r"pretrained"
            ],
            "project_reference": [
                r"github\.com.*project", r"demo", r"example", r"sample",
                r"reference", r"implementation"
            ],
            "service_provider": [
                r"aws", r"azure", r"gcp", r"cloud", r"hosting", r"saas",
                r"service", r"provider"
            ],
            "investor": [
                r"vc", r"venture", r"capital", r"investment", r"fund",
                r"investor", r"equity"
            ],
            "event": [
                r"event", r"conference", r"meetup", r"summit", r"workshop",
                r"webinar", r"symposium"
            ],
            "job": [
                r"job", r"career", r"hiring", r"recruit", r"position",
                r"linkedin\.com/jobs", r"indeed", r"glassdoor"
            ],
            "grant": [
                r"grant", r"funding", r"scholarship", r"fellowship",
                r"award", r"prize"
            ],
            "bounty": [
                r"bounty", r"reward", r"challenge", r"contest",
                r"hackathon", r"competition"
            ],
            "hardware": [
                r"hardware", r"gpu", r"cpu", r"chip", r"processor",
                r"nvidia", r"amd", r"intel"
            ],
            "news": [
                r"news", r"article", r"press", r"media", r"journalism",
                r"techcrunch", r"venturebeat", r"wired"
            ],
            "book": [
                r"book", r"ebook", r"pdf", r"amazon\.com.*book",
                r"goodreads", r"publisher"
            ],
            "podcast": [
                r"podcast", r"spotify\.com", r"apple\.com/podcasts",
                r"audio", r"listen"
            ],
            "platform": [
                r"platform", r"marketplace", r"hub", r"portal",
                r"ecosystem", r"infrastructure"
            ]
        }
    
    async def classify(self, entity: MinimalEntity) -> ClassificationResult:
        """
        Classify entity based on URL patterns.
        
        Args:
            entity: Entity to classify
            
        Returns:
            Classification result
        """
        url = str(entity.url).lower()
        name = entity.name.lower()
        
        # Combine URL and name for pattern matching
        text_to_analyze = f"{url} {name}"
        
        scores = {}
        
        # Check patterns for each entity type
        for entity_type, patterns in self.patterns.items():
            score = 0
            matched_patterns = []
            
            for pattern in patterns:
                if re.search(pattern, text_to_analyze):
                    score += 1
                    matched_patterns.append(pattern)
            
            if score > 0:
                # Normalize score based on number of patterns
                normalized_score = min(score / len(patterns), 1.0)
                scores[entity_type] = {
                    'score': normalized_score,
                    'matched_patterns': matched_patterns
                }
        
        if not scores:
            # Default to 'tool' if no patterns match
            return ClassificationResult(
                entity_type="tool",
                entity_type_id="00000000-0000-0000-0000-000000000000",  # Placeholder
                confidence=0.1,
                reasoning="No URL patterns matched, defaulting to tool",
            )
        
        # Get the highest scoring type
        best_type = max(scores.keys(), key=lambda k: scores[k]['score'])
        best_score = scores[best_type]['score']
        matched_patterns = scores[best_type]['matched_patterns']
        
        # Get alternative types
        alternative_types = [
            entity_type for entity_type, data in scores.items()
            if entity_type != best_type and data['score'] > 0.3
        ]
        
        return ClassificationResult(
            entity_type=best_type,
            entity_type_id="00000000-0000-0000-0000-000000000000",  # Placeholder
            confidence=best_score,
            reasoning=f"URL pattern classification: matched patterns {matched_patterns}",
            alternative_types=alternative_types,
        )


class NameBasedClassifier(BaseClassifier):
    """
    Classifier that uses entity names and keywords to determine type.
    """
    
    def __init__(self):
        super().__init__("name_based")
        
        # Define keywords for different entity types
        self.keywords = {
            "tool": [
                "ai", "ml", "gpt", "chat", "bot", "api", "platform", "app",
                "tool", "studio", "lab", "assistant", "generator", "analyzer",
                "prompt", "prompts", "promptbase", "prompthero", "promptlayer"
            ],
            "course": [
                "course", "learn", "education", "training", "tutorial",
                "academy", "school", "university", "class", "lesson"
            ],
            "agency": [
                "agency", "consulting", "services", "solutions", "studio",
                "digital", "marketing", "design", "firm", "company"
            ],
            "content_creator": [
                "blog", "blogger", "writer", "author", "creator", "influencer",
                "youtuber", "podcaster", "journalist"
            ],
            "community": [
                "community", "group", "club", "society", "forum", "discord",
                "slack", "reddit", "network"
            ],
            "newsletter": [
                "newsletter", "weekly", "daily", "digest", "update", "news",
                "bulletin", "report"
            ],
            "dataset": [
                "dataset", "data", "corpus", "collection", "database",
                "repository", "archive"
            ],
            "research_paper": [
                "paper", "research", "study", "analysis", "survey",
                "review", "thesis", "dissertation"
            ],
            "software": [
                "software", "library", "framework", "package", "sdk",
                "toolkit", "engine", "system"
            ],
            "model": [
                "model", "checkpoint", "weights", "pretrained", "neural",
                "network", "transformer", "bert", "gpt"
            ],
            "hardware": [
                "gpu", "cpu", "chip", "processor", "hardware", "device",
                "computer", "server", "workstation"
            ],
            "news": [
                "news", "article", "press", "media", "journalism",
                "breaking", "update", "report"
            ],
            "book": [
                "book", "ebook", "guide", "manual", "handbook",
                "textbook", "publication"
            ],
            "podcast": [
                "podcast", "audio", "listen", "episode", "show",
                "interview", "talk"
            ]
        }
    
    async def classify(self, entity: MinimalEntity) -> ClassificationResult:
        """
        Classify entity based on name and keywords.
        
        Args:
            entity: Entity to classify
            
        Returns:
            Classification result
        """
        name = entity.name.lower()
        
        scores = {}
        
        # Check keywords for each entity type
        for entity_type, keywords in self.keywords.items():
            score = 0
            matched_keywords = []
            
            for keyword in keywords:
                if keyword in name:
                    score += 1
                    matched_keywords.append(keyword)
            
            if score > 0:
                # Normalize score based on number of keywords
                normalized_score = min(score / len(keywords), 1.0)
                scores[entity_type] = {
                    'score': normalized_score,
                    'matched_keywords': matched_keywords
                }
        
        if not scores:
            # Default to 'tool' if no keywords match
            return ClassificationResult(
                entity_type="tool",
                entity_type_id="00000000-0000-0000-0000-000000000000",  # Placeholder
                confidence=0.1,
                reasoning="No keywords matched, defaulting to tool",
            )
        
        # Get the highest scoring type
        best_type = max(scores.keys(), key=lambda k: scores[k]['score'])
        best_score = scores[best_type]['score']
        matched_keywords = scores[best_type]['matched_keywords']
        
        # Get alternative types
        alternative_types = [
            entity_type for entity_type, data in scores.items()
            if entity_type != best_type and data['score'] > 0.2
        ]
        
        return ClassificationResult(
            entity_type=best_type,
            entity_type_id="00000000-0000-0000-0000-000000000000",  # Placeholder
            confidence=best_score,
            reasoning=f"Name-based classification: matched keywords {matched_keywords}",
            alternative_types=alternative_types,
        )


class EntityTypeClassifier:
    """
    Main entity type classifier that combines multiple classification strategies.
    """
    
    def __init__(self):
        """Initialize the entity type classifier."""
        self.classifiers = [
            URLPatternClassifier(),
            NameBasedClassifier(),
        ]
        
        # Weights for combining classifier results
        self.classifier_weights = {
            "url_pattern": 0.7,
            "name_based": 0.3,
        }
    
    async def classify(self, entity: MinimalEntity) -> ClassificationResult:
        """
        Classify an entity using multiple classifiers.
        
        Args:
            entity: Entity to classify
            
        Returns:
            Combined classification result
        """
        logger.debug(f"Classifying entity: {entity.name}")
        
        # Get results from all classifiers
        classifier_results = {}
        for classifier in self.classifiers:
            try:
                result = await classifier.classify(entity)
                classifier_results[classifier.name] = result
            except Exception as e:
                logger.error(f"Error in classifier {classifier.name}: {e}")
        
        if not classifier_results:
            # Fallback if all classifiers fail
            return ClassificationResult(
                entity_type="tool",
                entity_type_id="00000000-0000-0000-0000-000000000000",
                confidence=0.1,
                reasoning="All classifiers failed, using fallback",
            )
        
        # Combine results using weighted voting
        type_scores = {}
        reasoning_parts = []
        
        for classifier_name, result in classifier_results.items():
            weight = self.classifier_weights.get(classifier_name, 1.0)
            weighted_confidence = result.confidence * weight
            
            if result.entity_type not in type_scores:
                type_scores[result.entity_type] = 0
            
            type_scores[result.entity_type] += weighted_confidence
            reasoning_parts.append(f"{classifier_name}: {result.reasoning}")
        
        # Get the highest scoring type
        best_type = max(type_scores.keys(), key=lambda k: type_scores[k])
        best_score = type_scores[best_type]
        
        # Normalize the score
        max_possible_score = sum(self.classifier_weights.values())
        normalized_score = min(best_score / max_possible_score, 1.0)
        
        # Get alternative types
        alternative_types = [
            entity_type for entity_type, score in type_scores.items()
            if entity_type != best_type and score > 0.3
        ]
        
        combined_reasoning = "; ".join(reasoning_parts)
        
        logger.debug(f"Classification result for {entity.name}: {best_type} (confidence: {normalized_score:.2f})")
        
        return ClassificationResult(
            entity_type=best_type,
            entity_type_id="00000000-0000-0000-0000-000000000000",  # Will be mapped later
            confidence=normalized_score,
            reasoning=combined_reasoning,
            alternative_types=alternative_types,
        )
    
    def add_classifier(self, classifier: BaseClassifier, weight: float = 1.0):
        """
        Add a new classifier to the ensemble.
        
        Args:
            classifier: Classifier to add
            weight: Weight for this classifier in voting
        """
        self.classifiers.append(classifier)
        self.classifier_weights[classifier.name] = weight
        logger.info(f"Added classifier: {classifier.name} with weight {weight}")
    
    def remove_classifier(self, classifier_name: str) -> bool:
        """
        Remove a classifier by name.
        
        Args:
            classifier_name: Name of classifier to remove
            
        Returns:
            True if removed, False if not found
        """
        for i, classifier in enumerate(self.classifiers):
            if classifier.name == classifier_name:
                self.classifiers.pop(i)
                if classifier_name in self.classifier_weights:
                    del self.classifier_weights[classifier_name]
                logger.info(f"Removed classifier: {classifier_name}")
                return True
        return False
    
    def get_classifier_names(self) -> List[str]:
        """Get list of registered classifier names."""
        return [classifier.name for classifier in self.classifiers]


class ContentAnalysisClassifier(BaseClassifier):
    """
    Advanced classifier that analyzes webpage content for classification.
    Uses HTML structure, meta tags, and content analysis.
    """

    def __init__(self, timeout: int = 10):
        super().__init__("content_analysis")
        self.timeout = timeout

        # Content patterns for different entity types
        self.content_patterns = {
            "tool": {
                "meta_keywords": ["ai", "tool", "platform", "api", "software", "app"],
                "title_patterns": [r"ai", r"tool", r"platform", r"api", r"app"],
                "description_patterns": [r"artificial intelligence", r"machine learning", r"ai tool"],
                "html_indicators": ["pricing", "features", "demo", "try", "signup"],
            },
            "course": {
                "meta_keywords": ["course", "learn", "education", "training", "tutorial"],
                "title_patterns": [r"course", r"learn", r"training", r"tutorial"],
                "description_patterns": [r"learn", r"course", r"education", r"training"],
                "html_indicators": ["enroll", "lesson", "module", "certificate", "instructor"],
            },
            "agency": {
                "meta_keywords": ["agency", "consulting", "services", "digital", "marketing"],
                "title_patterns": [r"agency", r"consulting", r"services", r"studio"],
                "description_patterns": [r"agency", r"consulting", r"services", r"solutions"],
                "html_indicators": ["portfolio", "clients", "case studies", "contact us"],
            },
            "research_paper": {
                "meta_keywords": ["research", "paper", "study", "analysis", "academic"],
                "title_patterns": [r"paper", r"research", r"study", r"analysis"],
                "description_patterns": [r"research", r"study", r"analysis", r"academic"],
                "html_indicators": ["abstract", "authors", "citation", "doi", "arxiv"],
            },
            "software": {
                "meta_keywords": ["software", "library", "framework", "open source", "github"],
                "title_patterns": [r"library", r"framework", r"package", r"sdk"],
                "description_patterns": [r"library", r"framework", r"open source", r"github"],
                "html_indicators": ["documentation", "install", "github", "download", "api"],
            },
            "news": {
                "meta_keywords": ["news", "article", "breaking", "update", "press"],
                "title_patterns": [r"news", r"breaking", r"update", r"report"],
                "description_patterns": [r"news", r"article", r"breaking", r"update"],
                "html_indicators": ["published", "author", "date", "share", "comments"],
            },
            "book": {
                "meta_keywords": ["book", "ebook", "author", "publisher", "isbn"],
                "title_patterns": [r"book", r"ebook", r"guide", r"manual"],
                "description_patterns": [r"book", r"author", r"publisher", r"read"],
                "html_indicators": ["author", "publisher", "isbn", "buy", "read"],
            },
            "podcast": {
                "meta_keywords": ["podcast", "episode", "listen", "audio", "show"],
                "title_patterns": [r"podcast", r"episode", r"show", r"audio"],
                "description_patterns": [r"podcast", r"listen", r"episode", r"audio"],
                "html_indicators": ["listen", "episode", "subscribe", "spotify", "apple"],
            }
        }

    async def _fetch_page_content(self, url: str) -> Optional[Tuple[str, BeautifulSoup]]:
        """
        Fetch and parse webpage content.

        Args:
            url: URL to fetch

        Returns:
            Tuple of (raw_html, parsed_soup) or None if failed
        """
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        return None

                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    return html, soup

        except Exception as e:
            logger.debug(f"Error fetching content from {url}: {e}")
            return None

    def _analyze_meta_tags(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract and analyze meta tags."""
        meta_data = {}

        # Get title
        title_tag = soup.find('title')
        if title_tag:
            meta_data['title'] = title_tag.get_text().strip().lower()

        # Get meta description
        desc_tag = soup.find('meta', attrs={'name': 'description'})
        if desc_tag and desc_tag.get('content'):
            meta_data['description'] = desc_tag['content'].strip().lower()

        # Get meta keywords
        keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
        if keywords_tag and keywords_tag.get('content'):
            meta_data['keywords'] = keywords_tag['content'].strip().lower()

        # Get Open Graph data
        og_type = soup.find('meta', attrs={'property': 'og:type'})
        if og_type and og_type.get('content'):
            meta_data['og_type'] = og_type['content'].strip().lower()

        og_title = soup.find('meta', attrs={'property': 'og:title'})
        if og_title and og_title.get('content'):
            meta_data['og_title'] = og_title['content'].strip().lower()

        return meta_data

    def _analyze_content_structure(self, soup: BeautifulSoup) -> Dict[str, int]:
        """Analyze HTML structure for classification signals."""
        structure_signals = {}

        # Count specific HTML elements that indicate content type
        structure_signals['h1_count'] = len(soup.find_all('h1'))
        structure_signals['h2_count'] = len(soup.find_all('h2'))
        structure_signals['article_count'] = len(soup.find_all('article'))
        structure_signals['nav_count'] = len(soup.find_all('nav'))
        structure_signals['form_count'] = len(soup.find_all('form'))
        structure_signals['video_count'] = len(soup.find_all('video'))
        structure_signals['audio_count'] = len(soup.find_all('audio'))

        # Look for specific class names and IDs
        page_text = soup.get_text().lower()
        structure_signals['pricing_mentions'] = page_text.count('pricing') + page_text.count('price')
        structure_signals['feature_mentions'] = page_text.count('feature') + page_text.count('features')
        structure_signals['demo_mentions'] = page_text.count('demo') + page_text.count('try')

        return structure_signals

    async def classify(self, entity: MinimalEntity) -> ClassificationResult:
        """
        Classify entity based on content analysis.

        Args:
            entity: Entity to classify

        Returns:
            Classification result
        """
        # Fetch page content
        content_data = await self._fetch_page_content(str(entity.url))
        if not content_data:
            return ClassificationResult(
                entity_type="tool",
                entity_type_id="00000000-0000-0000-0000-000000000000",
                confidence=0.1,
                reasoning="Could not fetch page content for analysis",
            )

        html, soup = content_data

        # Analyze meta tags
        meta_data = self._analyze_meta_tags(soup)

        # Analyze content structure
        structure_signals = self._analyze_content_structure(soup)

        # Score each entity type
        type_scores = {}

        for entity_type, patterns in self.content_patterns.items():
            score = 0
            matched_signals = []

            # Check meta keywords
            if 'keywords' in meta_data:
                for keyword in patterns['meta_keywords']:
                    if keyword in meta_data['keywords']:
                        score += 0.2
                        matched_signals.append(f"meta_keyword:{keyword}")

            # Check title patterns
            if 'title' in meta_data:
                for pattern in patterns['title_patterns']:
                    if re.search(pattern, meta_data['title']):
                        score += 0.3
                        matched_signals.append(f"title_pattern:{pattern}")

            # Check description patterns
            if 'description' in meta_data:
                for pattern in patterns['description_patterns']:
                    if re.search(pattern, meta_data['description']):
                        score += 0.2
                        matched_signals.append(f"description_pattern:{pattern}")

            # Check HTML indicators
            page_text = soup.get_text().lower()
            for indicator in patterns['html_indicators']:
                if indicator in page_text:
                    score += 0.1
                    matched_signals.append(f"html_indicator:{indicator}")

            if score > 0:
                type_scores[entity_type] = {
                    'score': min(score, 1.0),  # Cap at 1.0
                    'signals': matched_signals
                }

        if not type_scores:
            return ClassificationResult(
                entity_type="tool",
                entity_type_id="00000000-0000-0000-0000-000000000000",
                confidence=0.2,
                reasoning="No content patterns matched, defaulting to tool",
            )

        # Get best classification
        best_type = max(type_scores.keys(), key=lambda k: type_scores[k]['score'])
        best_score = type_scores[best_type]['score']
        matched_signals = type_scores[best_type]['signals']

        # Get alternatives
        alternative_types = [
            entity_type for entity_type, data in type_scores.items()
            if entity_type != best_type and data['score'] > 0.3
        ]

        reasoning = f"Content analysis: {len(matched_signals)} signals matched: {matched_signals[:5]}"

        return ClassificationResult(
            entity_type=best_type,
            entity_type_id="00000000-0000-0000-0000-000000000000",
            confidence=best_score,
            reasoning=reasoning,
            alternative_types=alternative_types,
        )
