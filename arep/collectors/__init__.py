"""
Collectors package for the AI Resource Enhancement Pipeline.
Contains all data collection implementations including scrapers and API clients.
"""

# Import all collector modules to ensure they're registered
from . import arxiv, product_hunt
from .base import BaseCollector, BaseScraper, MockScraper
from .collector import (AdvancedDataCollector, MinimalDataCollector,
                        PluggableDataCollector)
from .registry import (get_all_collectors, get_collector_by_name,
                       list_available_collectors)

__all__ = [
    "BaseCollector",
    "BaseScraper",
    "MockScraper",
    "MinimalDataCollector",
    "AdvancedDataCollector",
    "PluggableDataCollector",
    "get_all_collectors",
    "get_collector_by_name",
    "list_available_collectors",
]
