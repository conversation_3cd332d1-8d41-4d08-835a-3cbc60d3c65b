"""
Minimal data collector that orchestrates different scrapers.
Coordinates the collection of entity data from multiple sources.
"""

import asyncio
from datetime import datetime
from itertools import chain
from typing import Dict, List, Optional, Type

from arep.collectors.base import BaseCollector, BaseScraper, MockScraper
from arep.models import CollectionMetrics, MinimalEntity, ScrapingResult
from arep.utils.favicon import FaviconExtractor
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class PluggableDataCollector:
    """
    New pluggable data collector that uses the BaseCollector registry system.
    Automatically discovers and runs all available collectors.
    """

    def __init__(self):
        """Initialize the pluggable data collector with auto-discovered collectors."""
        # Import here to avoid circular imports
        from .registry import get_all_collectors

        self.collectors = get_all_collectors()
        logger.info(
            f"Initialized with {len(self.collectors)} collectors: {[c.source_name for c in self.collectors]}"
        )

    async def collect(self) -> List[MinimalEntity]:
        """
        Runs all registered collectors concurrently and returns a deduplicated list of entities.

        Returns:
            List of MinimalEntity objects
        """
        logger.info("Starting data collection from all collectors")

        # Run all collectors concurrently
        tasks = [collector.run() for collector in self.collectors]
        results_nested = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and flatten the list
        all_entities = list(
            chain.from_iterable(res for res in results_nested if isinstance(res, list))
        )

        # Deduplicate based on URL
        seen_urls = set()
        deduplicated_entities = []
        for entity in all_entities:
            if entity.url not in seen_urls:
                seen_urls.add(entity.url)
                deduplicated_entities.append(entity)

        logger.info(
            f"Total collected entities: {len(all_entities)}. After deduplication: {len(deduplicated_entities)}"
        )
        return deduplicated_entities

    def get_collector_names(self) -> List[str]:
        """Get list of all collector source names."""
        return [collector.source_name for collector in self.collectors]


class MinimalDataCollector:
    """
    Orchestrates data collection from multiple scrapers.
    Manages scraper instances and coordinates the collection process.
    """

    def __init__(self, max_concurrent_scrapers: int = 5):
        """
        Initialize the data collector.

        Args:
            max_concurrent_scrapers: Maximum number of scrapers to run concurrently
        """
        self.max_concurrent_scrapers = max_concurrent_scrapers
        self.scrapers: List[BaseScraper] = []
        self.favicon_extractor = FaviconExtractor()
        self.metrics = CollectionMetrics()

        # Register default scrapers
        self._register_default_scrapers()

    def _register_default_scrapers(self):
        """Register default scrapers for testing and development."""
        # For now, just add the mock scraper
        # In the future, this will dynamically load real scrapers
        self.add_scraper(MockScraper())

    async def collect_with_new_system(self) -> List[MinimalEntity]:
        """
        Collect data using the new BaseCollector registry system.
        This provides access to the pluggable collector architecture.

        Returns:
            List of MinimalEntity objects from new collectors
        """
        # Import here to avoid circular imports
        from .registry import get_all_collectors

        collectors = get_all_collectors()
        logger.info(f"Using new collector system with {len(collectors)} collectors")

        # Run all collectors concurrently
        tasks = [collector.run() for collector in collectors]
        results_nested = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and flatten the list
        all_entities = list(
            chain.from_iterable(res for res in results_nested if isinstance(res, list))
        )

        # Deduplicate based on URL
        seen_urls = set()
        deduplicated_entities = []
        for entity in all_entities:
            if entity.url not in seen_urls:
                seen_urls.add(entity.url)
                deduplicated_entities.append(entity)

        logger.info(
            f"New system collected {len(all_entities)} entities, {len(deduplicated_entities)} after deduplication"
        )
        return deduplicated_entities

    def add_scraper(self, scraper: BaseScraper):
        """
        Add a scraper to the collection.

        Args:
            scraper: Scraper instance to add
        """
        self.scrapers.append(scraper)
        logger.info(f"Added scraper: {scraper.name}")

    def remove_scraper(self, scraper_name: str) -> bool:
        """
        Remove a scraper by name.

        Args:
            scraper_name: Name of the scraper to remove

        Returns:
            True if scraper was removed, False if not found
        """
        for i, scraper in enumerate(self.scrapers):
            if scraper.name == scraper_name:
                self.scrapers.pop(i)
                logger.info(f"Removed scraper: {scraper_name}")
                return True
        return False

    def get_scraper_names(self) -> List[str]:
        """Get list of registered scraper names."""
        return [scraper.name for scraper in self.scrapers]

    async def collect_from_scraper(self, scraper: BaseScraper) -> ScrapingResult:
        """
        Collect data from a single scraper.

        Args:
            scraper: Scraper to collect from

        Returns:
            ScrapingResult with collected data
        """
        logger.info(f"Starting collection from {scraper.name}")
        start_time = datetime.utcnow()

        try:
            async with scraper:
                result = await scraper.scrape()

                # Enhance entities with better favicon URLs if needed
                enhanced_entities = []
                for entity_data in result.entities_data:
                    if isinstance(entity_data, dict):
                        # Convert dict to MinimalEntity if needed
                        if "logo_url" not in entity_data or not entity_data["logo_url"]:
                            entity_data[
                                "logo_url"
                            ] = await self.favicon_extractor.get_favicon(
                                entity_data["url"]
                            )
                        enhanced_entities.append(entity_data)
                    else:
                        enhanced_entities.append(entity_data)

                result.entities_data = enhanced_entities

                # Update metrics
                self.metrics.total_entities_discovered += result.entities_found

                end_time = datetime.utcnow()
                processing_time = (end_time - start_time).total_seconds()

                logger.info(
                    f"Completed collection from {scraper.name}: "
                    f"{result.entities_found} entities in {processing_time:.2f}s"
                )

                return result

        except Exception as e:
            logger.error(f"Error collecting from {scraper.name}: {e}")
            return ScrapingResult(
                source_name=scraper.name,
                source_url=scraper.base_url,
                entities_found=0,
                entities_data=[],
                scraping_timestamp=datetime.utcnow(),
                success=False,
                error_message=str(e),
            )

    async def collect_all(
        self, scraper_names: Optional[List[str]] = None
    ) -> List[ScrapingResult]:
        """
        Collect data from all registered scrapers or specified scrapers.

        Args:
            scraper_names: Optional list of scraper names to run. If None, runs all.

        Returns:
            List of ScrapingResult objects
        """
        logger.info("Starting data collection from all scrapers")
        self.metrics.collection_start_time = datetime.utcnow()

        # Filter scrapers if specific names provided
        scrapers_to_run = self.scrapers
        if scraper_names:
            scrapers_to_run = [
                scraper for scraper in self.scrapers if scraper.name in scraper_names
            ]

        if not scrapers_to_run:
            logger.warning("No scrapers to run")
            return []

        # Create semaphore to limit concurrent scrapers
        semaphore = asyncio.Semaphore(self.max_concurrent_scrapers)

        async def run_scraper_with_semaphore(scraper):
            async with semaphore:
                return await self.collect_from_scraper(scraper)

        # Run scrapers concurrently
        tasks = [run_scraper_with_semaphore(scraper) for scraper in scrapers_to_run]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results and handle exceptions
        scraping_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(
                    f"Scraper {scrapers_to_run[i].name} failed with exception: {result}"
                )
                scraping_results.append(
                    ScrapingResult(
                        source_name=scrapers_to_run[i].name,
                        source_url=scrapers_to_run[i].base_url,
                        entities_found=0,
                        entities_data=[],
                        scraping_timestamp=datetime.utcnow(),
                        success=False,
                        error_message=str(result),
                    )
                )
            else:
                scraping_results.append(result)

        self.metrics.collection_end_time = datetime.utcnow()

        # Calculate metrics
        total_entities = sum(
            result.entities_found for result in scraping_results if result.success
        )
        successful_scrapers = sum(1 for result in scraping_results if result.success)

        logger.info(
            f"Collection completed: {total_entities} entities from "
            f"{successful_scrapers}/{len(scrapers_to_run)} scrapers"
        )

        return scraping_results

    async def collect(self) -> List[MinimalEntity]:
        """
        Collect minimal entity data from all sources.

        Returns:
            List of MinimalEntity objects
        """
        scraping_results = await self.collect_all()

        entities = []
        for result in scraping_results:
            if result.success:
                for entity_data in result.entities_data:
                    try:
                        if isinstance(entity_data, dict):
                            # Convert dict to MinimalEntity
                            entity = MinimalEntity(**entity_data)
                        else:
                            entity = entity_data

                        entities.append(entity)
                    except Exception as e:
                        logger.error(
                            f"Error creating entity from data {entity_data}: {e}"
                        )

        logger.info(f"Collected {len(entities)} minimal entities")
        return entities

    def get_metrics(self) -> CollectionMetrics:
        """Get collection metrics."""
        return self.metrics

    def get_scraper_metrics(self) -> Dict[str, dict]:
        """Get metrics for all scrapers."""
        return {scraper.name: scraper.get_metrics() for scraper in self.scrapers}

    def reset_metrics(self):
        """Reset collection metrics."""
        self.metrics = CollectionMetrics()
        for scraper in self.scrapers:
            scraper.requests_made = 0
            scraper.successful_requests = 0
            scraper.failed_requests = 0


class AdvancedDataCollector(MinimalDataCollector):
    """
    Advanced data collector with additional features like filtering and deduplication.
    """

    def __init__(
        self, max_concurrent_scrapers: int = 5, enable_deduplication: bool = True
    ):
        """
        Initialize the advanced data collector.

        Args:
            max_concurrent_scrapers: Maximum number of scrapers to run concurrently
            enable_deduplication: Whether to enable entity deduplication
        """
        super().__init__(max_concurrent_scrapers)
        self.enable_deduplication = enable_deduplication
        self.seen_urls = set()

    def _is_duplicate(self, entity: MinimalEntity) -> bool:
        """
        Check if an entity is a duplicate based on URL.

        Args:
            entity: Entity to check

        Returns:
            True if duplicate, False otherwise
        """
        if not self.enable_deduplication:
            return False

        url_str = str(entity.url).lower().rstrip("/")
        if url_str in self.seen_urls:
            return True

        self.seen_urls.add(url_str)
        return False

    async def collect(self) -> List[MinimalEntity]:
        """
        Collect minimal entity data with deduplication.

        Returns:
            List of unique MinimalEntity objects
        """
        entities = await super().collect()

        if not self.enable_deduplication:
            return entities

        # Reset seen URLs for this collection
        self.seen_urls.clear()

        # Filter duplicates
        unique_entities = []
        duplicates_removed = 0

        for entity in entities:
            if not self._is_duplicate(entity):
                unique_entities.append(entity)
            else:
                duplicates_removed += 1

        if duplicates_removed > 0:
            logger.info(f"Removed {duplicates_removed} duplicate entities")

        return unique_entities

    def clear_deduplication_cache(self):
        """Clear the deduplication cache."""
        self.seen_urls.clear()
