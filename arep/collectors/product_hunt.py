"""
Product Hunt scraper for collecting new AI tools and products.
Scrapes the Product Hunt homepage to discover trending tools.
"""

from datetime import datetime
from typing import List

import aiohttp
from bs4 import BeautifulSoup

from arep.collectors.base import BaseCollector
from arep.models import MinimalEntity
from arep.utils.favicon import get_favicon_url
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class ProductHuntScraper(BaseCollector):
    """
    Scraper for Product Hunt to collect new tools and products.
    Focuses on discovering trending AI and tech tools.
    """

    source_name = "product_hunt"
    PH_URL = "https://www.producthunt.com/"

    async def collect(self, session: aiohttp.ClientSession) -> List[MinimalEntity]:
        """
        Collect data from Product Hunt homepage.

        Args:
            session: aiohttp ClientSession for making requests

        Returns:
            List of MinimalEntity objects discovered from Product Hunt
        """
        logger.info(f"Collecting data from {self.source_name}")
        try:
            async with session.get(self.PH_URL) as response:
                if response.status != 200:
                    logger.error(f"Product Hunt returned status {response.status}")
                    return []

                html = await response.text()
                soup = BeautifulSoup(html, "lxml")

                entities = []

                # NOTE: These selectors are examples and WILL change.
                # You must inspect the page to get the current ones.
                # Product Hunt frequently updates their CSS classes.

                # Try multiple possible selectors for product posts
                post_selectors = [
                    "div[data-test='post-item']",  # Common test attribute
                    "div.styles_item__L_l01",  # From architectural plan
                    "article",  # Semantic HTML
                    "div[class*='post']",  # Any div with 'post' in class
                    "div[class*='item']",  # Any div with 'item' in class
                ]

                posts = []
                for selector in post_selectors:
                    posts = soup.select(selector)
                    if posts:
                        logger.info(
                            f"Found {len(posts)} posts using selector: {selector}"
                        )
                        break

                if not posts:
                    logger.warning(
                        "No posts found with any selector. Page structure may have changed."
                    )
                    return []

                for post in posts:
                    try:
                        # Try multiple selectors for product name
                        name_selectors = [
                            "h3",
                            "div.styles_title__x3_2K",  # From architectural plan
                            "[data-test='post-name']",
                            "a[class*='title']",
                            "div[class*='title']",
                            "span[class*='title']",
                        ]

                        name_tag = None
                        for selector in name_selectors:
                            name_tag = post.select_one(selector)
                            if name_tag:
                                break

                        # Try multiple selectors for product URL
                        url_selectors = [
                            "a[href*='/posts/']",
                            "a.styles_link__6f_3G",  # From architectural plan
                            "[data-test='post-link']",
                            "a[class*='link']",
                            "a[href]",
                        ]

                        url_tag = None
                        for selector in url_selectors:
                            url_tag = post.select_one(selector)
                            if url_tag and url_tag.get("href"):
                                break

                        if name_tag and url_tag and url_tag.get("href"):
                            name = name_tag.get_text(strip=True)
                            href = url_tag["href"]

                            # Ensure we have a complete URL
                            if href.startswith("/"):
                                temp_url = f"https://www.producthunt.com{href}"
                            elif href.startswith("http"):
                                temp_url = href
                            else:
                                temp_url = f"https://www.producthunt.com/{href}"

                            # Skip if name is too short or generic
                            if len(name) < 2 or name.lower() in [
                                "more",
                                "see all",
                                "view",
                                "click",
                            ]:
                                continue

                            entities.append(
                                MinimalEntity(
                                    name=name,
                                    url=temp_url,
                                    logo_url=get_favicon_url(temp_url),
                                    source=self.source_name,
                                    discovered_at=datetime.utcnow(),
                                )
                            )

                    except Exception as e:
                        logger.debug(f"Error processing individual post: {e}")
                        continue

                # Remove duplicates based on URL
                seen_urls = set()
                unique_entities = []
                for entity in entities:
                    if entity.url not in seen_urls:
                        seen_urls.add(entity.url)
                        unique_entities.append(entity)

                logger.info(
                    f"Collected {len(unique_entities)} unique entities from {self.source_name}"
                )
                return unique_entities

        except Exception as e:
            logger.exception(
                f"An error occurred during {self.source_name} scraping: {e}"
            )
            return []
