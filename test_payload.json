{"name": "[TEST] Live Integration Tool", "website_url": "https://example-test.com/live-integration", "short_description": "A temporary tool for live integration testing.", "logo_url": "https://www.google.com/s2/favicons?domain=example.com&sz=128", "status": "PENDING", "entity_type_id": "279a4f9b-9f10-4439-9bc9-a9aca497d2a0", "category_ids": ["bfc51175-6f22-47a9-a602-9d85c7aa340a"], "tag_ids": ["2cef8b3a-8760-4e55-ab73-2fd9993acfe3"], "tool_details": {"key_features": ["Integration testing", "Temporary entity", "Cleanup test"], "has_free_tier": true, "use_cases": ["Testing"], "integrations": ["API"], "programming_languages": ["Python"], "frameworks": ["FastAPI"], "libraries": ["aiohttp"], "target_audience": ["Developers"], "deployment_options": ["Cloud"], "supported_os": ["Linux"], "support_channels": ["Email"]}}