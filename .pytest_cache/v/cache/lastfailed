{"tests/test_pipeline.py::TestEnhancementPipeline": true, "tests/test_pipeline.py::TestEnhancementPipeline::test_single_entity_processing_success": true, "tests/test_pipeline.py::TestEnhancementPipeline::test_single_entity_processing_classification_failure": true, "tests/test_pipeline.py::TestEnhancementPipeline::test_single_entity_processing_unsupported_type": true, "tests/test_pipeline.py::TestEnhancementPipeline::test_full_pipeline_run_with_entities": true, "tests/test_pipeline.py::TestEnhancementPipeline::test_concurrent_processing": true, "tests/test_enhancers.py::TestToolEnhancer::test_tool_enhancement_success": true, "tests/test_enhancers.py::TestCourseEnhancer::test_course_enhancement_success": true, "tests/test_enhancers.py::TestCourseEnhancer::test_instructor_name_extraction": true, "tests/test_enhancers.py::TestCourseEnhancer::test_duration_extraction": true, "tests/test_enhancers.py::TestCourseEnhancer::test_skill_level_determination": true, "tests/test_enhancers.py::TestCourseEnhancer::test_certificate_detection": true, "tests/test_enhancers.py::TestCourseEnhancer::test_enrollment_count_extraction": true, "tests/test_research_engine.py::TestSmartResearchEngine::test_research_success": true, "tests/test_research_engine.py::TestSmartResearchEngine::test_research_http_error": true, "tests/test_research_engine.py::TestSmartResearchEngine::test_research_exception_handling": true, "tests/test_research_engine.py::TestSmartResearchEngine::test_website_research_success": true, "tests/test_e2e_full_flow.py::test_product_hunt_html_parsing_e2e": true}