#!/bin/bash

# Live Integration Test Runner
# This script helps run the live integration tests with proper environment setup

set -e  # Exit on any error

echo "🚀 AI Navigator Live Integration Test Runner"
echo "============================================="

# Load environment variables from .env file if it exists
if [ -f ".env" ]; then
    echo "📄 Loading environment variables from .env file..."
    export $(grep -v '^#' .env | xargs)
fi

# Check if required environment variables are set
if [ -z "$AI_NAV_API_URL" ]; then
    echo "❌ Error: AI_NAV_API_URL environment variable is not set"
    echo "   Please set it to your staging API URL, e.g.:"
    echo "   export AI_NAV_API_URL='https://ai-nav-staging.onrender.com'"
    exit 1
fi

if [ -z "$AI_NAV_AUTH_TOKEN" ]; then
    echo "❌ Error: AI_NAV_AUTH_TOKEN environment variable is not set"
    echo "   Please set it to your staging authentication token, e.g.:"
    echo "   export AI_NAV_AUTH_TOKEN='your-staging-auth-token'"
    exit 1
fi

echo "✅ Environment variables configured:"
echo "   API URL: $AI_NAV_API_URL"
echo "   Auth Token: ${AI_NAV_AUTH_TOKEN:0:10}... (truncated for security)"
echo ""

# Confirm with user before running
echo "⚠️  WARNING: These tests will create and attempt to delete test data in your staging database."
echo "   Make sure you're pointing to a STAGING environment, not production!"
echo ""
read -p "Do you want to continue? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Test run cancelled by user"
    exit 1
fi

echo ""
echo "🧪 Running live integration tests..."
echo "=================================="

# Set the flag to enable live tests and run them
export RUN_LIVE_TESTS=true
export PYTHONPATH="$(pwd):$PYTHONPATH"

# Run the tests with verbose output
if command -v pytest &> /dev/null; then
    echo "Using pytest to run tests..."
    pytest tests/test_live_integration.py -v -s
else
    echo "pytest not found, running tests directly with Python..."
    python tests/test_live_integration.py
fi

echo ""
echo "✅ Live integration tests completed!"
echo ""
echo "📝 Important Notes:"
echo "   - If any test entities were created, check the test output for cleanup instructions"
echo "   - Test entities are prefixed with '[TEST]' for easy identification"
echo "   - Consider implementing a delete_resource method in AINavigatorClient for automatic cleanup"
