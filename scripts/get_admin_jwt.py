#!/usr/bin/env python3
"""
Helper script to authenticate with AI Navigator API and get admin JWT token.

This script uses admin email/password credentials to obtain a JWT token
that can be used for API testing.
"""

import os
import asyncio
import aiohttp
import json
from dotenv import load_dotenv

load_dotenv()

async def get_admin_jwt():
    """
    Authenticate with the AI Navigator API and get an admin JWT token.
    """
    api_url = os.getenv("AI_NAV_API_URL", "https://ai-nav.onrender.com")
    admin_email = os.getenv("AI_NAVIGATOR_ADMIN_EMAIL")
    admin_password = os.getenv("AI_NAVIGATOR_ADMIN_PASSWORD")
    
    if not admin_email or not admin_password:
        print("❌ Error: Admin credentials not found in .env file")
        print("   Required: AI_NAVIGATOR_ADMIN_EMAIL and AI_NAVIGATOR_ADMIN_PASSWORD")
        return None
    
    print(f"🔐 Authenticating with AI Navigator API...")
    print(f"   API URL: {api_url}")
    print(f"   Admin Email: {admin_email}")
    
    # Try different possible authentication endpoints
    auth_endpoints = [
        "/auth/login",
        "/api/auth/login", 
        "/login",
        "/admin/login",
        "/api/admin/login"
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint in auth_endpoints:
            try:
                auth_url = f"{api_url.rstrip('/')}{endpoint}"
                print(f"\n🔍 Trying endpoint: {auth_url}")
                
                # Prepare login payload
                login_data = {
                    "email": admin_email,
                    "password": admin_password
                }
                
                headers = {
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
                
                async with session.post(
                    auth_url,
                    json=login_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    print(f"   Response status: {response.status}")
                    
                    if response.status == 200:
                        try:
                            data = await response.json()
                            print(f"   Response data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                            
                            # Look for JWT token in various possible fields
                            token_fields = ['token', 'access_token', 'jwt', 'authToken', 'accessToken']
                            jwt_token = None

                            # First check top-level fields
                            for field in token_fields:
                                if isinstance(data, dict) and field in data:
                                    jwt_token = data[field]
                                    print(f"   ✅ Found JWT token in field: {field}")
                                    break

                            # If not found, check nested session object
                            if not jwt_token and isinstance(data, dict) and 'session' in data:
                                session_data = data['session']
                                for field in token_fields:
                                    if isinstance(session_data, dict) and field in session_data:
                                        jwt_token = session_data[field]
                                        print(f"   ✅ Found JWT token in session.{field}")
                                        break
                            
                            if jwt_token:
                                # Verify the token looks like a JWT (has 3 parts separated by dots)
                                if isinstance(jwt_token, str) and jwt_token.count('.') == 2:
                                    print(f"   ✅ Valid JWT format detected")
                                    print(f"   Token preview: {jwt_token[:50]}...")
                                    return jwt_token
                                else:
                                    print(f"   ⚠️  Token doesn't look like JWT: {jwt_token}")
                            else:
                                print(f"   ⚠️  No JWT token found in response")
                                print(f"   Full response: {json.dumps(data, indent=2)}")
                        
                        except json.JSONDecodeError:
                            response_text = await response.text()
                            print(f"   ⚠️  Non-JSON response: {response_text[:200]}...")
                    
                    elif response.status == 404:
                        print(f"   ⚠️  Endpoint not found, trying next...")
                        continue
                    
                    else:
                        response_text = await response.text()
                        print(f"   ❌ Authentication failed: {response_text[:200]}...")
                        
            except aiohttp.ClientError as e:
                print(f"   ❌ Connection error: {e}")
                continue
            except Exception as e:
                print(f"   ❌ Unexpected error: {e}")
                continue
    
    print(f"\n❌ Failed to authenticate with any endpoint")
    print(f"   Tried endpoints: {auth_endpoints}")
    return None

async def update_env_file(jwt_token):
    """
    Update the .env file with the obtained JWT token.
    """
    env_file = ".env"
    
    try:
        # Read current .env file
        with open(env_file, 'r') as f:
            lines = f.readlines()
        
        # Update the JWT token line
        updated = False
        for i, line in enumerate(lines):
            if line.startswith('AI_NAV_AUTH_TOKEN='):
                lines[i] = f'AI_NAV_AUTH_TOKEN="{jwt_token}"\n'
                updated = True
                break
        
        if not updated:
            # Add the token if it doesn't exist
            lines.append(f'AI_NAV_AUTH_TOKEN="{jwt_token}"\n')
        
        # Write back to file
        with open(env_file, 'w') as f:
            f.writelines(lines)
        
        print(f"✅ Updated {env_file} with JWT token")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update {env_file}: {e}")
        return False

async def main():
    """
    Main function to get JWT token and update .env file.
    """
    print("🚀 AI Navigator Admin JWT Token Retriever")
    print("=" * 50)
    
    # Get JWT token
    jwt_token = await get_admin_jwt()
    
    if jwt_token:
        print(f"\n✅ Successfully obtained JWT token!")
        
        # Ask user if they want to update .env file
        response = input("\nDo you want to update the .env file with this token? (y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            success = await update_env_file(jwt_token)
            if success:
                print("\n🎉 Ready to run live integration tests!")
                print("   You can now run: ./scripts/run_live_tests.sh")
            else:
                print(f"\n⚠️  Please manually update your .env file:")
                print(f"   AI_NAV_AUTH_TOKEN=\"{jwt_token}\"")
        else:
            print(f"\n📋 Manual update required:")
            print(f"   Add this to your .env file:")
            print(f"   AI_NAV_AUTH_TOKEN=\"{jwt_token}\"")
    else:
        print(f"\n❌ Could not obtain JWT token")
        print(f"   Please check:")
        print(f"   1. Admin credentials are correct")
        print(f"   2. API URL is accessible")
        print(f"   3. Authentication endpoint exists")

if __name__ == "__main__":
    asyncio.run(main())
