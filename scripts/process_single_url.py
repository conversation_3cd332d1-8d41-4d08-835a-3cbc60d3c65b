#!/usr/bin/env python3
"""
Single Entity Processing Script for Manual QA

This script processes a single URL through the complete AREP pipeline:
1. Creates a minimal entity from the URL
2. Classifies the entity type
3. Researches the entity using real APIs
4. Enhances the entity with type-specific data
5. Submits to the staging database
6. Provides a link to inspect the result

Usage:
    python scripts/process_single_url.py "https://www.producthunt.com/posts/some-tool"

Environment Variables Required:
    - AI_NAV_API_URL: Staging API URL
    - AI_NAV_AUTH_TOKEN: Admin JWT token for API authentication
    - OPENAI_API_KEY: OpenAI API key (if using OpenAI for research)
"""

import asyncio
import argparse
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

# Add project root to Python path to enable imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from arep.models import MinimalEntity, ClassifiedEntity
from arep.classification.classifier import EntityTypeClassifier
from arep.enhancement.research import SmartResearchEngine
from arep.enhancement.registry import enhancer_registry
from arep.api.client import AINavigatorClient
from arep.utils.logger import get_logger

logger = get_logger(__name__)


async def main(url: str):
    """
    Main function to process a single URL through the entire real pipeline.
    
    Args:
        url: The URL to process
    """
    print(f"🚀 Starting full processing for URL: {url}")
    print("=" * 60)

    try:
        # 1. Create a Minimal Entity to start the process
        print("\n📝 Step 1: Creating minimal entity...")
        minimal_entity = MinimalEntity(
            name="[Pending Discovery]", 
            url=url, 
            source="manual_single_run",
            discovered_at=datetime.utcnow()
        )
        print(f"   Initial entity: {minimal_entity.name}")
        print(f"   URL: {minimal_entity.url}")
        print(f"   Source: {minimal_entity.source}")

        # 2. Classify the entity
        print("\n🔍 Step 2: Classifying entity type...")
        classifier = EntityTypeClassifier()
        classification = await classifier.classify(minimal_entity)
        
        # Map classification result fields to ClassifiedEntity fields
        classification_data = classification.model_dump()
        classified_entity = ClassifiedEntity(
            **minimal_entity.model_dump(),
            entity_type=classification_data["entity_type"],
            entity_type_id=classification_data["entity_type_id"],
            classification_confidence=classification_data["confidence"],
            classification_reasoning=classification_data.get("reasoning"),
            alternative_types=classification_data.get("alternative_types", [])
        )
        
        print(f"   ✅ Classified as: {classified_entity.entity_type}")
        print(f"   Confidence: {classified_entity.classification_confidence:.2f}")
        print(f"   Reasoning: {classified_entity.classification_reasoning}")
        
        if classified_entity.alternative_types:
            print(f"   Alternative types: {', '.join(classified_entity.alternative_types)}")

        # 3. Research the entity
        print("\n🔬 Step 3: Researching entity...")
        print("   This may take a moment as we fetch real data...")
        
        async with SmartResearchEngine() as research_engine:
            research_data = await research_engine.research(classified_entity)
        
        print(f"   ✅ Research complete!")
        print(f"   Short description length: {len(research_data.short_description) if research_data.short_description else 0} chars")
        print(f"   Description length: {len(research_data.description) if research_data.description else 0} chars")
        print(f"   Features found: {len(research_data.features) if research_data.features else 0}")
        print(f"   Categories found: {len(research_data.categories) if research_data.categories else 0}")
        print(f"   Research sources: {len(research_data.research_sources) if research_data.research_sources else 0}")

        # Show discovered name if found
        if research_data.name and research_data.name.strip():
            print(f"   📝 Discovered entity name: {research_data.name}")
        else:
            print(f"   ⚠️  No entity name discovered, using placeholder")

        # 4. Enhance the entity
        print("\n✨ Step 4: Enhancing entity with type-specific data...")
        enhancer = enhancer_registry.get_enhancer(classified_entity.entity_type)
        
        if not enhancer:
            print(f"   ❌ No enhancer found for type: {classified_entity.entity_type}")
            print(f"   Available enhancers: {enhancer_registry.get_supported_types()}")
            return
        
        print(f"   Using enhancer: {enhancer.__class__.__name__}")
        enhanced_resource = await enhancer.enhance(classified_entity, research_data)
        
        print(f"   ✅ Enhancement complete!")
        print(f"   Final entity name: '{enhanced_resource.name}'")
        print(f"   Short description: {enhanced_resource.short_description}")
        
        # Show type-specific details
        if hasattr(enhanced_resource, 'tool_details') and enhanced_resource.tool_details:
            tool_details = enhanced_resource.tool_details
            print(f"   Tool details:")
            print(f"     - Key features: {len(tool_details.key_features) if tool_details.key_features else 0}")
            print(f"     - Has API: {tool_details.has_api}")
            print(f"     - Has free tier: {tool_details.has_free_tier}")
            print(f"     - Use cases: {len(tool_details.use_cases) if tool_details.use_cases else 0}")

        # 5. Submit to staging database
        print("\n📡 Step 5: Submitting to staging database...")
        print("   Making API request...")
        
        async with AINavigatorClient() as api_client:
            result = await api_client.submit_resource(enhanced_resource)

        if result.success:
            print(f"   ✅ SUCCESS! Entity created in database")
            print(f"   Entity ID: {result.entity_id}")
            print(f"   Status: {result.status}")
            print(f"   Message: {result.message}")
            
            # Provide inspection guidance
            print(f"\n👉 Next steps for manual QA:")
            print(f"   1. Check your staging database for entity ID: {result.entity_id}")
            print(f"   2. Review the enhanced data quality")
            print(f"   3. Verify all fields are populated correctly")
            print(f"   4. Test the entity in your frontend if available")
            
        else:
            print(f"   ❌ FAILED to submit entity")
            print(f"   Message: {result.message}")
            if result.errors:
                print(f"   Errors:")
                for error in result.errors:
                    print(f"     - {error.error}: {error.message}")

    except Exception as e:
        logger.exception("An unexpected error occurred during the pipeline run")
        print(f"\n❌ ERROR: {e}")
        print("Check the logs for more details.")
        return 1

    print("\n🏁 Pipeline finished successfully!")
    print("=" * 60)
    return 0


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Process a single URL through the AREP pipeline for manual QA.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python scripts/process_single_url.py "https://www.producthunt.com/posts/chatgpt"
    python scripts/process_single_url.py "https://example-ai-tool.com"

Environment Variables:
    AI_NAV_API_URL      Staging API URL (required)
    AI_NAV_AUTH_TOKEN   Admin JWT token (required)
    OPENAI_API_KEY      OpenAI API key (optional, for enhanced research)
        """
    )
    parser.add_argument(
        "url", 
        type=str, 
        help="The full URL of the entity to process"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Set log level if verbose
    if args.verbose:
        import logging
        logging.getLogger("arep").setLevel(logging.DEBUG)
    
    # Validate URL format
    if not args.url.startswith(("http://", "https://")):
        print("❌ Error: URL must start with http:// or https://")
        sys.exit(1)
    
    # Run the pipeline
    exit_code = asyncio.run(main(args.url))
    sys.exit(exit_code)
