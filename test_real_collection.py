#!/usr/bin/env python3
"""
Test script to verify the pluggable system works with real data collection.
This will make actual HTTP requests to test the collectors.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from arep.collectors import PluggableDataCollector
from arep.utils.logger import get_logger

logger = get_logger(__name__)


async def test_real_collection():
    """Test the pluggable system with real data collection."""
    print("🌐 Testing real data collection...")
    print("This will make actual HTTP requests to Product Hunt and ArXiv")

    collector = PluggableDataCollector()

    try:
        print(f"\n📡 Starting collection from {len(collector.collectors)} collectors...")
        entities = await collector.collect()

        print(f"\n✅ Successfully collected {len(entities)} entities!")

        # Group by source
        by_source = {}
        for entity in entities:
            if entity.source not in by_source:
                by_source[entity.source] = []
            by_source[entity.source].append(entity)

        print("\n📊 Results by source:")
        for source, source_entities in by_source.items():
            print(f"  {source}: {len(source_entities)} entities")

            # Show first few entities from each source
            for i, entity in enumerate(source_entities[:3]):
                print(f"    {i+1}. {entity.name}")
                print(f"       URL: {entity.url}")
                if entity.logo_url:
                    print(f"       Logo: {entity.logo_url}")
                print()

            if len(source_entities) > 3:
                print(f"    ... and {len(source_entities) - 3} more")
                print()

        return entities

    except Exception as e:
        logger.error(f"Collection failed: {e}")
        print(f"❌ Collection failed: {e}")
        return []


async def main():
    """Run the real collection test."""
    print("🧪 Real Data Collection Test")
    print("=" * 40)

    # Ask for confirmation since this makes real HTTP requests
    print("⚠️  This test will make real HTTP requests to:")
    print("   • Product Hunt (https://www.producthunt.com/)")
    print("   • ArXiv API (http://export.arxiv.org/api/)")
    print()

    response = input("Continue? (y/N): ").strip().lower()
    if response != "y":
        print("Test cancelled.")
        return

    entities = await test_real_collection()

    if entities:
        print(f"\n🎉 Test completed successfully!")
        print(f"   Collected {len(entities)} unique entities")
        print("   The pluggable system is working correctly!")
    else:
        print("\n⚠️  No entities collected. This might be due to:")
        print("   • Network issues")
        print("   • Website structure changes")
        print("   • Rate limiting")
        print("   Check the logs for more details.")


if __name__ == "__main__":
    asyncio.run(main())
